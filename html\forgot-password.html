<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PPMS - Forgot Password</title>
    <!-- Bootstrap CSS -->
    <link href="../css/bootstrap/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- PPMS Custom Styles -->
    <link rel="stylesheet" href="../css/ppms-styles/shared/variables.css">
    <link rel="stylesheet" href="../css/ppms-styles/shared/typography.css">
    <link rel="stylesheet" href="../css/ppms-styles/shared/safe-typography-enhancements.css">
    <link rel="stylesheet" href="../css/ppms-styles/shared/components.css">
    <link rel="stylesheet" href="../css/ppms-styles/auth/login.css">
    <!-- Favicon -->
    <link rel="icon" href="../assets/Icon Web.ico" type="image/x-icon">
</head>
<body class="receiver-auth-body">
    <div class="receiver-auth-container">
        <!-- Left Panel -->
        <div class="receiver-left-panel">
            <h2>Forgot Your Password?</h2>
            <img src="../assets/stafflogin.gif" alt="Forgot Password Illustration">
            <p>"Don't worry, we'll help you get back in!"</p>
        </div>

        <!-- Right Panel (Form) -->
        <div class="receiver-right-panel">
            <!-- Sticky Header -->
            <div class="receiver-header-section">
                <h2>Reset Password</h2>
                <p class="receiver-welcome-text">Enter your IC number to reset your password</p>
            </div>

            <!-- Scrollable Form Content -->
            <div class="receiver-form-content">

                <form id="forgotPasswordForm" class="w-100">
                    <!-- IC Number Field -->
                    <div class="receiver-form-group">
                        <label for="ic_number" class="receiver-form-label">IC Number</label>
                        <div class="position-relative">
                            <i class="fas fa-id-card receiver-input-icon"></i>
                            <input
                                type="text"
                                class="receiver-form-control"
                                id="ic_number"
                                name="ic_number"
                                placeholder="Enter your IC Number (e.g., 123456-78-9012)"
                                pattern="^\d{6}-\d{2}-\d{4}$"
                                title="Please enter a valid Malaysian IC number format: 123456-78-9012"
                                required>
                        </div>
                        <small class="form-text text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            Enter your IC number in format: 123456-78-9012
                        </small>
                    </div>

                    <!-- Submit Button -->
                    <button type="submit" class="receiver-btn-primary">
                        <i class="fas fa-paper-plane me-2"></i>
                        Send Reset Instructions
                    </button>
                </form>
            </div>

            <!-- Back to Login Links -->
            <div class="receiver-auth-links">
                <p>
                    Remember your password?
                    <a href="receiver-login.html">Back to Login</a>
                </p>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="../js/bootstrap/bootstrap.bundle.min.js"></script>
    
    <script>
        // IC Number validation and formatting
        const icInput = document.getElementById('ic_number');

        // Format IC number as user types
        icInput.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, ''); // Remove non-digits

            // Format as 123456-78-9012
            if (value.length >= 6) {
                value = value.substring(0, 6) + '-' + value.substring(6);
            }
            if (value.length >= 9) {
                value = value.substring(0, 9) + '-' + value.substring(9, 13);
            }

            e.target.value = value;

            // Validate format
            validateICFormat();
        });

        // Validate IC format
        function validateICFormat() {
            const icValue = icInput.value;
            const icPattern = /^\d{6}-\d{2}-\d{4}$/;

            if (icValue && !icPattern.test(icValue)) {
                icInput.setCustomValidity('Please enter a valid Malaysian IC number format: 123456-78-9012');
            } else {
                icInput.setCustomValidity('');
            }
        }

        // Handle form submission
        document.getElementById('forgotPasswordForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const icNumber = document.getElementById('ic_number').value;

            // Validate IC format before submission
            if (!icNumber.match(/^\d{6}-\d{2}-\d{4}$/)) {
                Swal.fire({
                    icon: 'error',
                    title: 'Invalid IC Number',
                    text: 'Please enter a valid Malaysian IC number in format: 123456-78-9012',
                    confirmButtonColor: '#667eea'
                });
                return;
            }

            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;

            // Show loading state
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Sending...';
            submitBtn.disabled = true;

            // Create form data with only IC number (receiver only)
            const formData = new FormData();
            formData.append('ic_number', icNumber);
            formData.append('user_type', 'receiver'); // Force receiver type

            fetch('../php/forgot-password.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Reset Instructions Sent!',
                        text: data.message,
                        confirmButtonColor: '#667eea'
                    }).then(() => {
                        // Redirect back to receiver login
                        window.location.href = 'receiver-login.html';
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: data.message,
                        confirmButtonColor: '#667eea'
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Network Error',
                    text: 'Please check your connection and try again.',
                    confirmButtonColor: '#667eea'
                });
            })
            .finally(() => {
                // Restore button state
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            });
        });
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PPMS - Forgot Password</title>
    <!-- Bootstrap CSS -->
    <link href="../css/bootstrap/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- PPMS Custom Styles -->
    <link rel="stylesheet" href="../css/ppms-styles/shared/variables.css">
    <link rel="stylesheet" href="../css/ppms-styles/shared/typography.css">
    <link rel="stylesheet" href="../css/ppms-styles/shared/safe-typography-enhancements.css">
    <link rel="stylesheet" href="../css/ppms-styles/shared/components.css">
    <link rel="stylesheet" href="../css/ppms-styles/auth/login.css">
    <!-- Favicon -->
    <link rel="icon" href="../assets/Icon Web.ico" type="image/x-icon">
</head>
<body class="staff-auth-body">
    <div class="staff-auth-container">
        <!-- Left Panel -->
        <div class="staff-left-panel">
            <h2>Forgot Your Password?</h2>
            <img src="../assets/stafflogin.gif" alt="Forgot Password Illustration">
            <p>"Don't worry, it happens to the best of us!"</p>
        </div>

        <!-- Right Panel (Form) -->
        <div class="staff-right-panel">
            <!-- Sticky Header -->
            <div class="staff-header-section">
                <h2>Reset Password</h2>
                <p class="staff-welcome-text">Choose your account type and enter your credentials</p>
            </div>

            <!-- Scrollable Form Content -->
            <div class="staff-form-content">

                <form id="forgotPasswordForm" class="w-100">
                    <!-- User Type Selection -->
                    <div class="staff-form-group">
                        <label class="staff-form-label">Account Type</label>
                        <div class="user-type-selection">
                            <input type="radio" name="user_type" id="staff_type" value="staff" checked>
                            <label for="staff_type" class="user-type-option staff-option">
                                <i class="fas fa-user-tie"></i>
                                <span>Staff</span>
                            </label>

                            <input type="radio" name="user_type" id="receiver_type" value="receiver">
                            <label for="receiver_type" class="user-type-option receiver-option">
                                <i class="fas fa-user"></i>
                                <span>Receiver</span>
                            </label>
                        </div>
                    </div>

                    <!-- User ID Field -->
                    <div class="staff-form-group">
                        <label for="user_id" class="staff-form-label">
                            <span id="id_label">Staff ID</span>
                        </label>
                        <div class="position-relative">
                            <i id="id_icon" class="fas fa-id-badge staff-input-icon"></i>
                            <input
                                type="text"
                                class="staff-form-control"
                                id="user_id"
                                name="user_id"
                                placeholder="Enter your Staff ID"
                                required>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <button type="submit" class="staff-btn-primary">
                        <i class="fas fa-paper-plane me-2"></i>
                        Send Reset Instructions
                    </button>
                </form>
            </div>

            <!-- Back to Login Links -->
            <div class="staff-auth-links">
                <p>
                    Remember your password?
                    <a href="#" id="back-to-login-link">Back to Login</a>
                </p>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="../js/bootstrap/bootstrap.bundle.min.js"></script>
    
    <script>
        // Update form and styling based on user type selection
        document.querySelectorAll('input[name="user_type"]').forEach(radio => {
            radio.addEventListener('change', function() {
                const userIdInput = document.getElementById('user_id');
                const idLabel = document.getElementById('id_label');
                const idIcon = document.getElementById('id_icon');
                const backToLoginLink = document.getElementById('back-to-login-link');
                const body = document.body;
                const container = document.querySelector('.staff-auth-container, .receiver-auth-container');
                const leftPanel = document.querySelector('.staff-left-panel, .receiver-left-panel');
                const rightPanel = document.querySelector('.staff-right-panel, .receiver-right-panel');
                const headerSection = document.querySelector('.staff-header-section, .receiver-header-section');
                const formContent = document.querySelector('.staff-form-content, .receiver-form-content');
                const authLinks = document.querySelector('.staff-auth-links, .receiver-auth-links');
                const formGroups = document.querySelectorAll('.staff-form-group, .receiver-form-group');
                const formLabels = document.querySelectorAll('.staff-form-label, .receiver-form-label');
                const submitBtn = document.querySelector('.staff-btn-primary, .receiver-btn-primary');
                const welcomeText = document.querySelector('.staff-welcome-text, .receiver-welcome-text');

                if (this.value === 'staff') {
                    // Staff styling
                    body.className = 'staff-auth-body';
                    container.className = 'staff-auth-container';
                    leftPanel.className = 'staff-left-panel';
                    rightPanel.className = 'staff-right-panel';
                    headerSection.className = 'staff-header-section';
                    formContent.className = 'staff-form-content';
                    authLinks.className = 'staff-auth-links';
                    welcomeText.className = 'staff-welcome-text';

                    formGroups.forEach(group => group.className = 'staff-form-group');
                    formLabels.forEach(label => label.className = 'staff-form-label');
                    submitBtn.className = 'staff-btn-primary';

                    idLabel.textContent = 'Staff ID';
                    userIdInput.placeholder = 'Enter your Staff ID';
                    userIdInput.className = 'staff-form-control';
                    idIcon.className = 'fas fa-id-badge staff-input-icon';
                    backToLoginLink.href = 'staff-login.html';
                } else {
                    // Receiver styling
                    body.className = 'receiver-auth-body';
                    container.className = 'receiver-auth-container';
                    leftPanel.className = 'receiver-left-panel';
                    rightPanel.className = 'receiver-right-panel';
                    headerSection.className = 'receiver-header-section';
                    formContent.className = 'receiver-form-content';
                    authLinks.className = 'receiver-auth-links';
                    welcomeText.className = 'receiver-welcome-text';

                    formGroups.forEach(group => group.className = 'receiver-form-group');
                    formLabels.forEach(label => label.className = 'receiver-form-label');
                    submitBtn.className = 'receiver-btn-primary';

                    idLabel.textContent = 'IC Number';
                    userIdInput.placeholder = 'Enter your IC Number';
                    userIdInput.className = 'receiver-form-control';
                    idIcon.className = 'fas fa-id-card receiver-input-icon';
                    backToLoginLink.href = 'receiver-login.html';
                }
                userIdInput.value = '';
            });
        });

        // Initialize with staff styling
        document.getElementById('back-to-login-link').href = 'staff-login.html';

        // Handle form submission
        document.getElementById('forgotPasswordForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            
            // Show loading state
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Sending...';
            submitBtn.disabled = true;
            
            fetch('../php/forgot-password.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Reset Instructions Sent!',
                        text: data.message,
                        confirmButtonColor: '#6A1B9A'
                    }).then(() => {
                        // In production, redirect to login
                        // For demo, show the token
                        if (data.demo_token) {
                            window.location.href = `reset-password.html?token=${data.demo_token}`;
                        }
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: data.message,
                        confirmButtonColor: '#6A1B9A'
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Network Error',
                    text: 'Please check your connection and try again.',
                    confirmButtonColor: '#6A1B9A'
                });
            })
            .finally(() => {
                // Restore button state
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            });
        });
    </script>
</body>
</html>

# PPMS (Perwira Parcel Management System) - Complete Backup

**Backup Date:** June 24, 2025  
**System Version:** Complete PPMS with all features  
**Database:** MariaDB/MySQL  

## 📦 What's Included in This Backup

### 🗂️ **Complete File Structure:**
```
ppms/
├── html/                          # Main application files
│   ├── index.html                 # Landing page with video background
│   ├── receiver-login.php         # Receiver login interface
│   ├── receiver-register.php      # Receiver registration
│   ├── receiver-dashboard.php     # Receiver dashboard (track parcels, notifications)
│   ├── staff-login.php           # Staff/Admin login interface
│   ├── staff-register.php        # Staff registration
│   └── staff-dashboard.php       # Staff dashboard (full CRUD operations)
├── php/                          # Backend PHP scripts
│   ├── db_connect.php            # Database connection
│   ├── *-login.php               # Authentication handlers
│   ├── *-register.php            # Registration handlers
│   ├── admin-*.php               # Admin/Staff operations
│   ├── receiver-*.php            # Receiver operations
│   └── notification-helper.php   # Notification system
├── css/                          # Styling files
│   ├── bootstrap/                # Bootstrap framework
│   └── ppms-styles/              # Custom PPMS styles
│       ├── auth/                 # Login/register styles
│       ├── receiver/             # Receiver interface styles
│       ├── staff/                # Staff interface styles
│       ├── shared/               # Shared components
│       └── landing.css           # Landing page styles
├── assets/                       # Media files
│   ├── *.png, *.jpg, *.webp      # Delivery partner logos
│   ├── background-video.mp4      # Landing page video
│   └── icons/                    # System icons
└── js/                          # JavaScript files
    └── qrcode.min.js            # QR code generation
```

### 🗄️ **Database Structure:**
- **receiver** - User accounts for parcel recipients
- **staff** - Staff and Admin accounts
- **parcel** - Parcel information and tracking
- **notification** - Notification system
- **retrievalrecord** - Parcel pickup records

## 🚀 **System Features**

### **Receiver Interface:**
✅ User registration with IC validation  
✅ Secure login with password hashing  
✅ Parcel tracking by IC number  
✅ Real-time notifications  
✅ QR code verification  
✅ Modern, responsive design  

### **Staff Interface:**
✅ Staff and Admin roles  
✅ Complete CRUD operations (Create, Read, Update, Delete)  
✅ Parcel management system  
✅ QR code generation  
✅ Notification management  
✅ Real-time statistics  

### **Security Features:**
✅ Password salting and hashing (bcrypt)  
✅ Session management  
✅ SQL injection prevention  
✅ Role-based access control (RBAC)  
✅ Input validation and sanitization  

### **Design Features:**
✅ Modern, sleek UI design  
✅ Responsive layout (mobile-friendly)  
✅ Consistent color schemes  
✅ Smooth animations and transitions  
✅ Professional typography  

## 🔧 **Installation Instructions**

### **Prerequisites:**
- XAMPP (Apache + MySQL/MariaDB + PHP)
- Web browser (Chrome, Firefox, Edge)

### **Step 1: Extract Files**
1. Extract the `ppms/` folder to `C:\xampp\htdocs\`
2. Ensure the structure is: `C:\xampp\htdocs\ppms\`

### **Step 2: Database Setup**
1. Start XAMPP (Apache + MySQL)
2. Open phpMyAdmin: `http://localhost/phpmyadmin`
3. Create database: `CREATE DATABASE ppms;`
4. Import database: Import `ppms_database_backup.sql`

### **Step 3: Configuration**
1. Check `php/db_connect.php` for database settings:
   ```php
   $servername = "localhost";
   $username = "root";
   $password = "";
   $dbname = "ppms";
   ```

### **Step 4: Access System**
- **Landing Page:** `http://localhost/ppms/`
- **Receiver Login:** `http://localhost/ppms/html/receiver-login.php`
- **Staff Login:** `http://localhost/ppms/html/staff-login.php`

## 👥 **Default Accounts**

### **Admin Account:**
- **Username:** admin
- **Password:** admin123
- **Role:** Administrator (full access)

### **Test Receiver Account:**
- **IC:** ************
- **Password:** MikailTest123!
- **Name:** Mikail

### **Test Staff Account:**
- **Staff ID:** 0105
- **Password:** StaffTest123!
- **Role:** Staff

## 🎨 **Design Specifications**

### **Color Schemes:**
- **Receiver Interface:** Purple to Green gradient
- **Staff Interface:** Purple to Orange gradient
- **Shared Elements:** Modern minimalist design

### **Typography:**
- Engaging, student-friendly fonts
- Consistent hierarchy and spacing
- Professional appearance

## 📊 **Database Schema**

### **Key Relationships:**
- `parcel.ICNumber` → `receiver.ICNumber`
- `notification.ICNumber` → `receiver.ICNumber`
- `notification.TrackingNumber` → `parcel.TrackingNumber`
- `retrievalrecord.trackingNumber` → `parcel.TrackingNumber`
- `retrievalrecord.ICNumber` → `receiver.ICNumber`
- `retrievalrecord.staffID` → `staff.staffID`

## 🔒 **Security Notes**

1. **Change default passwords** before production use
2. **Update database credentials** in `php/db_connect.php`
3. **Enable HTTPS** for production deployment
4. **Regular database backups** recommended
5. **Keep PHP and MySQL updated**

## 📞 **Support Information**

**System Name:** Perwira Parcel Management System (PPMS)  
**Institution:** Universiti Tun Hussein Onn Malaysia (UTHM)  
**Purpose:** Parcel management for Perwira College  

## 📝 **Version History**

- **v1.0** - Initial system with basic functionality
- **v2.0** - Added notification system and QR codes
- **v3.0** - Enhanced UI/UX and security features
- **v4.0** - Complete CRUD operations and role management
- **Current** - Fully functional system with all features

---

**⚠️ Important:** This backup contains a complete, working PPMS system. All features have been tested and are fully functional. Follow the installation instructions carefully for best results.

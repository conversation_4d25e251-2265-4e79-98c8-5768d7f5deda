/* ===================================
   PPMS Staff Navbar & Button Styles
   Navbar fixes and button enhancements
   ================================== */

/* === Navbar Gap Fix and Positioning === */
/* Ensure navbar has proper z-index and NO gaps */
.navbar-custom {
    z-index: 1050 !important;
    position: relative !important;
    margin: 0 !important;
    padding: 1rem 2rem !important;
    margin-top: 0 !important;
    margin-bottom: 0 !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    border: 0 !important;
    outline: 0 !important;
}

/* KILL the ::before pseudo-element that's causing the gap! */
.navbar-custom::before,
.navbar-custom::after,
nav.navbar-custom::before,
nav.navbar-custom::after {
    content: none !important;
    display: none !important;
    height: 0 !important;
    width: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
    border: 0 !important;
}

/* Force Font Awesome icons to work */
.fas::before,
.far::before,
.fab::before,
.fa::before,
i.fas::before,
i.far::before,
i.fab::before,
i.fa::before {
    display: inline-block !important;
    font-style: normal !important;
    font-variant: normal !important;
    text-rendering: auto !important;
    -webkit-font-smoothing: antialiased !important;
}

/* Specific Font Awesome font families */
.fas, i.fas {
    font-family: "Font Awesome 6 Free" !important;
    font-weight: 900 !important;
}

.far, i.far {
    font-family: "Font Awesome 6 Free" !important;
    font-weight: 400 !important;
}

.fab, i.fab {
    font-family: "Font Awesome 6 Brands" !important;
    font-weight: 400 !important;
}

.fa, i.fa {
    font-family: "Font Awesome 6 Free" !important;
    font-weight: 900 !important;
}

/* Force navbar to stick to top edge */
body > nav:first-of-type {
    margin-top: 0 !important;
    padding-top: 1rem !important;
}

/* Ensure dropdown container has proper stacking */
.dropdown {
    z-index: 1051 !important;
    position: relative !important;
}

/* Prevent overflow issues and remove default spacing */
body {
    overflow-x: visible !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* Ensure navbar doesn't clip dropdown */
.navbar-custom {
    overflow: visible !important;
}

/* === Button Styling Consistency === */
/* Fix button styling consistency with staff theme */
.btn-primary {
    background: linear-gradient(135deg, #6A1B9A 0%, #FF9800 100%) !important;
    border: none !important;
    padding: 0.75rem 2rem !important;
    font-weight: 600 !important;
    border-radius: 0.5rem !important;
    transition: all 0.3s ease !important;
    color: white !important;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5e1688 0%, #e68900 100%) !important;
    box-shadow: 0 8px 25px rgba(106, 27, 154, 0.5) !important;
    transform: translateY(-2px) !important;
    color: white !important;
}

.btn-primary:focus {
    background: linear-gradient(135deg, #6A1B9A 0%, #FF9800 100%) !important;
    box-shadow: 0 0 0 3px rgba(106, 27, 154, 0.25) !important;
    color: white !important;
}

.btn-primary:active {
    background: linear-gradient(135deg, #FF9800 0%, #e68900 100%) !important;
    transform: translateY(0) !important;
    color: white !important;
}

/* Fix icon alignment in buttons */
.btn-primary i.fas.fa-search {
    margin-right: 0.5rem !important;
    font-size: 0.9rem !important;
    vertical-align: middle !important;
}

/* Custom styling for Sort By and Refresh buttons */
.btn-outline-primary {
    border-color: #6A1B9A !important;
    color: #6A1B9A !important;
    font-weight: 600 !important;
    padding: 0.5rem 1.25rem !important;
    border-radius: 0.5rem !important;
    transition: all 0.3s ease !important;
}

.btn-outline-primary:hover {
    background: linear-gradient(135deg, #6A1B9A 0%, #FF9800 100%) !important;
    border-color: #6A1B9A !important;
    color: white !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(106, 27, 154, 0.3) !important;
}

.btn-outline-primary:focus {
    background: linear-gradient(135deg, #6A1B9A 0%, #FF9800 100%) !important;
    border-color: #6A1B9A !important;
    color: white !important;
    box-shadow: 0 0 0 3px rgba(106, 27, 154, 0.25) !important;
}

.btn-outline-primary:active {
    background: linear-gradient(135deg, #FF9800 0%, #e68900 100%) !important;
    border-color: #FF9800 !important;
    color: white !important;
    transform: translateY(0) !important;
}

/* === Dropdown Menu Styling === */
.dropdown-menu {
    border: 1px solid rgba(106, 27, 154, 0.2) !important;
    border-radius: 0.75rem !important;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1) !important;
    backdrop-filter: blur(10px) !important;
}

.dropdown-item {
    padding: 0.75rem 1.25rem !important;
    transition: all 0.3s ease !important;
    border-radius: 0.5rem !important;
    margin: 0.25rem !important;
}

.dropdown-item:hover {
    background: rgba(106, 27, 154, 0.1) !important;
    color: #6A1B9A !important;
    transform: translateX(5px) !important;
}

.dropdown-item i {
    color: #6A1B9A !important;
}

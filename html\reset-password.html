<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PPMS - Reset Password</title>
    <!-- Bootstrap CSS -->
    <link href="../css/bootstrap/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- PPMS Custom Styles -->
    <link rel="stylesheet" href="../css/ppms-styles/shared/variables.css">
    <link rel="stylesheet" href="../css/ppms-styles/shared/typography.css">
    <link rel="stylesheet" href="../css/ppms-styles/shared/safe-typography-enhancements.css">
    <link rel="stylesheet" href="../css/ppms-styles/shared/password-validation.css">
    <link rel="stylesheet" href="../css/ppms-styles/shared/components.css">
    <link rel="stylesheet" href="../css/ppms-styles/auth/register.css">
    <!-- Favicon -->
    <link rel="icon" href="../assets/Icon Web.ico" type="image/x-icon">
</head>
<body class="staff-auth-body">
    <div class="staff-auth-container">
        <!-- Left Panel -->
        <div class="staff-left-panel">
            <h2>Create New Password</h2>
            <img src="../assets/parcelsecure.gif" alt="Reset Password Illustration">
            <p>"Almost there! Set your new secure password"</p>
        </div>

        <!-- Right Panel (Form) -->
        <div class="staff-right-panel">
            <!-- Sticky Header -->
            <div class="staff-header-section">
                <h2>Set New Password</h2>
                <p class="staff-welcome-text">Choose a strong password for your account</p>
            </div>

            <!-- Scrollable Form Content -->
            <div class="staff-form-content">
                <form id="resetPasswordForm" class="w-100">
                    <input type="hidden" id="reset_token" name="token">

                    <!-- New Password Field -->
                    <div class="staff-form-group">
                        <label for="new_password" class="staff-form-label">New Password</label>
                        <div class="password-field-container">
                            <i class="fas fa-lock staff-input-icon"></i>
                            <input
                                type="password"
                                class="staff-form-control"
                                id="new_password"
                                name="new_password"
                                placeholder="Create a strong password"
                                required
                                minlength="12">

                            <!-- Password Requirements Popup -->
                            <div class="password-requirements">
                            <small>Password must:
                                <button type="button" class="password-requirements-close" onclick="this.closest('.password-requirements').classList.remove('show')">
                                    <i class="fas fa-times"></i>
                                </button>
                            </small>
                            <ul class="password-checklist">
                                <li class="requirement" data-requirement="length">
                                    <div class="requirement-icon invalid"></div>
                                    Have at least 12 characters
                                </li>
                                <li class="requirement" data-requirement="uppercase">
                                    <div class="requirement-icon invalid"></div>
                                    Include uppercase letters (A-Z)
                                </li>
                                <li class="requirement" data-requirement="lowercase">
                                    <div class="requirement-icon invalid"></div>
                                    Include lowercase letters (a-z)
                                </li>
                                <li class="requirement" data-requirement="number">
                                    <div class="requirement-icon invalid"></div>
                                    Include numbers (0-9)
                                </li>
                                <li class="requirement" data-requirement="special">
                                    <div class="requirement-icon invalid"></div>
                                    Include special characters (@$!%*?&)
                                </li>
                            </ul>
                        </div>

                        <i class="fas fa-eye staff-password-toggle" onclick="togglePassword('new_password', this)"></i>
                        </div>
                    </div>

                    <!-- Confirm Password Field -->
                    <div class="staff-form-group">
                        <label for="confirm_password" class="staff-form-label">Confirm New Password</label>
                        <div class="position-relative">
                            <i class="fas fa-lock staff-input-icon"></i>
                            <input
                                type="password"
                                class="staff-form-control"
                                id="confirm_password"
                                name="confirm_password"
                                placeholder="Confirm your new password"
                                required>
                            <i class="fas fa-eye staff-password-toggle" onclick="togglePassword('confirm_password', this)"></i>
                        </div>
                        <div id="password-match" class="mt-2"></div>
                    </div>

                    <!-- Submit Button -->
                    <button type="submit" class="staff-btn-primary" id="resetBtn" disabled>
                        <i class="fas fa-check me-2"></i>
                        Reset Password
                    </button>
                </form>
            </div>

            <!-- Back to Login Link -->
            <div class="staff-auth-links">
                <p>
                    Need help?
                    <a href="forgot-password.html">Back to Forgot Password</a>
                </p>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="../js/bootstrap/bootstrap.bundle.min.js"></script>
    
    <script>
        // Get token from URL and determine user type
        const urlParams = new URLSearchParams(window.location.search);
        const token = urlParams.get('token');

        if (!token) {
            Swal.fire({
                icon: 'error',
                title: 'Invalid Link',
                text: 'This password reset link is invalid or has expired.',
                confirmButtonColor: '#6A1B9A'
            }).then(() => {
                window.location.href = 'forgot-password.html';
            });
        } else {
            document.getElementById('reset_token').value = token;

            // Determine user type from session or token (for demo purposes, we'll use staff styling by default)
            // In production, you might want to include user type in the token or check session
            applyUserTypeStyles('staff'); // Default to staff styling
        }

        // Apply styling based on user type
        function applyUserTypeStyles(userType) {
            const body = document.body;
            const container = document.querySelector('.staff-auth-container, .receiver-auth-container');
            const leftPanel = document.querySelector('.staff-left-panel, .receiver-left-panel');
            const rightPanel = document.querySelector('.staff-right-panel, .receiver-right-panel');
            const headerSection = document.querySelector('.staff-header-section, .receiver-header-section');
            const formContent = document.querySelector('.staff-form-content, .receiver-form-content');
            const authLinks = document.querySelector('.staff-auth-links, .receiver-auth-links');
            const formGroups = document.querySelectorAll('.staff-form-group, .receiver-form-group');
            const formLabels = document.querySelectorAll('.staff-form-label, .receiver-form-label');
            const formControls = document.querySelectorAll('.staff-form-control, .receiver-form-control');
            const submitBtn = document.querySelector('.staff-btn-primary, .receiver-btn-primary');
            const welcomeText = document.querySelector('.staff-welcome-text, .receiver-welcome-text');

            if (userType === 'staff') {
                body.className = 'staff-auth-body';
                container.className = 'staff-auth-container';
                leftPanel.className = 'staff-left-panel';
                rightPanel.className = 'staff-right-panel';
                headerSection.className = 'staff-header-section';
                formContent.className = 'staff-form-content';
                authLinks.className = 'staff-auth-links';
                welcomeText.className = 'staff-welcome-text';

                formGroups.forEach(group => group.className = 'staff-form-group');
                formLabels.forEach(label => label.className = 'staff-form-label');
                formControls.forEach(control => control.className = 'staff-form-control');
                submitBtn.className = 'staff-btn-primary';
            } else {
                body.className = 'receiver-auth-body';
                container.className = 'receiver-auth-container';
                leftPanel.className = 'receiver-left-panel';
                rightPanel.className = 'receiver-right-panel';
                headerSection.className = 'receiver-header-section';
                formContent.className = 'receiver-form-content';
                authLinks.className = 'receiver-auth-links';
                welcomeText.className = 'receiver-welcome-text';

                formGroups.forEach(group => group.className = 'receiver-form-group');
                formLabels.forEach(label => label.className = 'receiver-form-label');
                formControls.forEach(control => control.className = 'receiver-form-control');
                submitBtn.className = 'receiver-btn-primary';
            }
        }

        // Password visibility toggle
        function togglePassword(fieldId, icon) {
            const field = document.getElementById(fieldId);
            if (field.type === 'password') {
                field.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                field.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        }

        // Password validation using PPMS system
        function validatePassword() {
            const password = document.getElementById('new_password').value;
            const confirmPassword = document.getElementById('confirm_password').value;
            const resetBtn = document.getElementById('resetBtn');

            // Check requirements using PPMS validation system
            const requirements = {
                length: password.length >= 12,
                uppercase: /[A-Z]/.test(password),
                lowercase: /[a-z]/.test(password),
                number: /\d/.test(password),
                special: /[@$!%*?&]/.test(password)
            };

            // Update requirement indicators using PPMS system
            Object.keys(requirements).forEach(req => {
                const element = document.querySelector(`[data-requirement="${req}"]`);
                const icon = element.querySelector('.requirement-icon');
                if (requirements[req]) {
                    icon.className = 'requirement-icon valid';
                } else {
                    icon.className = 'requirement-icon invalid';
                }
            });

            // Check password match
            const matchDiv = document.getElementById('password-match');
            if (confirmPassword && password !== confirmPassword) {
                matchDiv.innerHTML = '<small class="text-danger"><i class="fas fa-times"></i> Passwords do not match</small>';
            } else if (confirmPassword && password === confirmPassword) {
                matchDiv.innerHTML = '<small class="text-success"><i class="fas fa-check"></i> Passwords match</small>';
            } else {
                matchDiv.innerHTML = '';
            }

            // Enable/disable submit button
            const allValid = Object.values(requirements).every(req => req) &&
                           confirmPassword && password === confirmPassword;
            resetBtn.disabled = !allValid;

            // Show/hide password requirements popup
            const passwordField = document.getElementById('new_password');
            const requirementsPopup = document.querySelector('.password-requirements');
            if (password.length > 0 && !Object.values(requirements).every(req => req)) {
                requirementsPopup.classList.add('show');
            } else {
                requirementsPopup.classList.remove('show');
            }
        }

        // Add event listeners
        document.getElementById('new_password').addEventListener('input', validatePassword);
        document.getElementById('confirm_password').addEventListener('input', validatePassword);

        // Handle form submission
        document.getElementById('resetPasswordForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const submitBtn = document.getElementById('resetBtn');
            const originalText = submitBtn.innerHTML;
            
            // Show loading state
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Resetting...';
            submitBtn.disabled = true;
            
            fetch('../php/reset-password.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Password Reset Successful!',
                        text: data.message,
                        confirmButtonColor: '#6A1B9A'
                    }).then(() => {
                        window.location.href = data.redirect;
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: data.message,
                        confirmButtonColor: '#6A1B9A'
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Network Error',
                    text: 'Please check your connection and try again.',
                    confirmButtonColor: '#6A1B9A'
                });
            })
            .finally(() => {
                // Restore button state
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            });
        });
    </script>
</body>
</html>

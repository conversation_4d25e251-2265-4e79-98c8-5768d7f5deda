/* ===================================
   PPMS Shared Components
   ================================== */

/* === Base Styles === */
body {
    background: var(--light-bg);
    font-family: var(--font-family-primary);
    line-height: var(--line-height-relaxed);
    color: var(--text-primary);
    margin: 0;
    padding: 0;
}

/* === Modern Buttons === */
.btn-primary {
    background: var(--primary-gradient);
    border: none;
    border-radius: var(--radius-lg);
    padding: var(--btn-padding-md);
    font-weight: var(--font-weight-semibold);
    transition: var(--transition);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    color: var(--text-white);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    color: var(--text-white);
}

.btn-secondary {
    background: var(--secondary-gradient);
    border: none;
    border-radius: var(--radius-lg);
    padding: var(--btn-padding-md);
    font-weight: var(--font-weight-semibold);
    transition: var(--transition);
    box-shadow: 0 4px 15px rgba(240, 147, 251, 0.3);
    color: var(--text-white);
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(240, 147, 251, 0.4);
    color: var(--text-white);
}

.btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    background: transparent;
    border-radius: var(--radius-lg);
    padding: var(--btn-padding-md);
    font-weight: var(--font-weight-semibold);
    transition: var(--transition);
}

.btn-outline-primary:hover {
    background: var(--primary-gradient);
    border-color: transparent;
    color: var(--text-white);
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-outline-secondary {
    border: 2px solid var(--border-light);
    color: var(--text-muted);
    background: transparent;
    border-radius: var(--radius-lg);
    padding: var(--btn-padding-md);
    font-weight: var(--font-weight-semibold);
    transition: var(--transition);
}

.btn-outline-secondary:hover {
    background: var(--bg-light);
    border-color: var(--border-gray);
    transform: translateY(-1px);
}

.logout-btn {
    background: var(--secondary-gradient);
    border: none;
    color: var(--text-white);
    padding: var(--btn-padding-md);
    border-radius: var(--radius-lg);
    font-weight: var(--font-weight-semibold);
    transition: var(--transition);
    box-shadow: 0 4px 15px rgba(245, 87, 108, 0.3);
}

.logout-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(245, 87, 108, 0.4);
    color: var(--text-white);
}

/* === Modern Cards === */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-card);
    transition: var(--transition);
    overflow: hidden;
    background: var(--bg-white);
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-card-hover);
}

.card-header {
    background: var(--primary-gradient);
    border: none;
    padding: var(--spacing-xl);
    position: relative;
    color: var(--text-white);
}

.card-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1), transparent);
}

.card-body {
    padding: var(--spacing-xl);
}

/* === Modern Form Elements === */
.form-control {
    border: 2px solid var(--border-light);
    border-radius: var(--radius-lg);
    padding: var(--input-padding);
    font-weight: var(--font-weight-medium);
    transition: var(--transition);
    background: rgba(255, 255, 255, 0.8);
    height: var(--input-height);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: var(--input-focus-ring);
    background: var(--bg-white);
    outline: none;
}

.form-label {
    font-weight: var(--font-weight-semibold);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
}

/* === Modern Tables === */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
    background: var(--bg-white);
}

.table-light {
    background: linear-gradient(135deg, var(--bg-light) 0%, var(--bg-gray-50) 100%);
}

.table th {
    font-weight: var(--font-weight-bold);
    color: var(--text-secondary);
    border: none;
    padding: var(--spacing-md) var(--spacing-xl);
}

.table td {
    border: none;
    padding: var(--spacing-md) var(--spacing-xl);
    vertical-align: middle;
}

.table-hover tbody tr:hover {
    background: rgba(102, 126, 234, 0.05);
    transform: scale(1.01);
    transition: var(--transition);
}

/* === Modern Badges === */
.badge {
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-2xl);
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-xs);
    letter-spacing: 0.5px;
}

.bg-warning {
    background: var(--warning-gradient) !important;
    color: var(--dark-color) !important;
}

.bg-success {
    background: var(--success-gradient) !important;
    color: var(--text-white) !important;
}

.bg-danger {
    background: var(--danger-gradient) !important;
    color: var(--text-white) !important;
}

.bg-primary {
    background: var(--primary-gradient) !important;
    color: var(--text-white) !important;
}

/* === Modern Alerts === */
.alert {
    border: none;
    border-radius: var(--border-radius);
    padding: var(--spacing-lg) var(--spacing-xl);
    font-weight: var(--font-weight-medium);
}

.alert-warning {
    background: linear-gradient(135deg, rgba(251, 191, 36, 0.1), rgba(245, 158, 11, 0.1));
    color: #92400e;
    border-left: 4px solid #f59e0b;
}

.alert-danger {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(220, 38, 38, 0.1));
    color: #991b1b;
    border-left: 4px solid #dc2626;
}

.alert-info {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(37, 99, 235, 0.1));
    color: #1e40af;
    border-left: 4px solid #3b82f6;
}

.alert-success {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.1), rgba(22, 163, 74, 0.1));
    color: #166534;
    border-left: 4px solid #16a34a;
}

/* === Dropdown Enhancements === */
.dropdown-menu {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-card);
    padding: var(--spacing-sm);
}

.dropdown-item {
    border-radius: var(--radius-md);
    padding: var(--spacing-sm) var(--spacing-md);
    font-weight: var(--font-weight-medium);
    transition: var(--transition);
}

.dropdown-item:hover {
    background: rgba(102, 126, 234, 0.1);
    color: var(--primary-color);
    transform: translateX(4px);
}

/* === Loading States === */
.btn-loading {
    position: relative;
    pointer-events: none;
}

.btn-loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    margin: auto;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* === Utility Classes === */
.text-gradient-primary {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.shadow-card {
    box-shadow: var(--shadow-card);
}

.shadow-card-hover {
    box-shadow: var(--shadow-card-hover);
}

.border-radius {
    border-radius: var(--border-radius);
}

.transition {
    transition: var(--transition);
}

/* === Modern Logout Dialog Styling === */
.modern-logout-popup {
    border-radius: 20px !important;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15) !important;
    border: none !important;
    padding: 2rem !important;
    max-width: 420px !important;
}

.modern-loading-popup {
    border-radius: 16px !important;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1) !important;
    border: none !important;
    padding: 1.5rem !important;
    max-width: 350px !important;
}

/* Modern Button Styling */
.modern-confirm-btn {
    background: linear-gradient(135deg, #667eea 0%, #43e97b 100%) !important;
    border: none !important;
    border-radius: 12px !important;
    padding: 0.75rem 1.5rem !important;
    font-weight: 600 !important;
    font-size: 0.95rem !important;
    color: white !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3) !important;
    margin: 0 0.5rem !important;
}

.modern-confirm-btn:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4) !important;
    background: linear-gradient(135deg, #5a67d8 0%, #38d9a9 100%) !important;
}

.modern-cancel-btn {
    background: #f9fafb !important;
    border: 2px solid #e5e7eb !important;
    border-radius: 12px !important;
    padding: 0.75rem 1.5rem !important;
    font-weight: 600 !important;
    font-size: 0.95rem !important;
    color: #6b7280 !important;
    transition: all 0.3s ease !important;
    margin: 0 0.5rem !important;
}

.modern-cancel-btn:hover {
    background: #f3f4f6 !important;
    border-color: #d1d5db !important;
    color: #374151 !important;
    transform: translateY(-1px) !important;
}

/* SweetAlert2 Overrides */
.swal2-popup {
    font-family: 'Inter', 'Poppins', sans-serif !important;
    border: none !important;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15) !important;
}

.swal2-actions {
    margin-top: 2rem !important;
    gap: 0.5rem !important;
}

/* Force remove any borders from SweetAlert icons */
.swal2-icon {
    border: none !important;
    box-shadow: none !important;
}

.swal2-icon-content {
    border: none !important;
}

/* Custom icon container overrides */
.modern-logout-popup .swal2-icon,
.modern-loading-popup .swal2-icon {
    border: none !important;
    background: transparent !important;
    box-shadow: none !important;
}

/* Shimmer Animation for Icon */
@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

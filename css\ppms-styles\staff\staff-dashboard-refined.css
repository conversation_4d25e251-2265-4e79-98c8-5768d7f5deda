/* ===================================
   PPMS Staff Dashboard Refined Styles
   ================================== */

/* === Global Styles === */
body {
    background: linear-gradient(135deg, #6A1B9A 0%, #FF9800 100%);
    background-attachment: fixed;
    min-height: 100vh;
    margin: 0;
    padding: 0;
}

/* === Navbar Specific === */
.navbar-custom {
    background: var(--dark-gradient);
    color: var(--text-white);
    padding: var(--spacing-xl) var(--spacing-2xl);
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: var(--shadow-lg);
    position: relative;
    overflow: hidden;
}

.navbar-custom::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(135deg, #6A1B9A 0%, #FF9800 100%);
}

.navbar-brand {
    font-weight: var(--font-weight-bold);
    font-size: var(--font-size-lg);
    letter-spacing: 0.5px;
}

.navbar-welcome {
    font-weight: var(--font-weight-medium);
    opacity: var(--opacity-hover);
}

/* === Dashboard Container === */
.dashboard-container {
    max-width: var(--container-max-width);
    margin: var(--spacing-2xl) auto;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
    padding: var(--spacing-2xl) var(--spacing-3xl);
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: var(--transition);
    min-height: 80vh;
    width: 95%;
}

.dashboard-container:hover {
    box-shadow: 0 15px 50px rgba(0, 0, 0, 0.12);
}

/* === Modern Tabs === */
.nav-tabs {
    border: none;
    margin-bottom: var(--spacing-2xl);
    background: rgba(255, 255, 255, 0.9);
    border-radius: var(--border-radius);
    padding: var(--spacing-sm);
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.nav-tabs .nav-link {
    color: #64748b !important;
    border: none;
    border-radius: var(--radius-lg);
    margin-right: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-xl);
    font-weight: 600 !important;
    font-size: 0.95rem !important;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    text-shadow: none !important;
}

.nav-tabs .nav-link:hover:not(.active) {
    background: rgba(106, 27, 154, 0.1) !important;
    color: #6A1B9A !important;
    transform: translateY(-1px);
    text-shadow: none !important;
}

.nav-tabs .nav-link.active {
    background: linear-gradient(135deg, #6A1B9A 0%, #FF9800 100%) !important;
    color: white !important;
    box-shadow: 0 4px 20px rgba(106, 27, 154, 0.4) !important;
    transform: translateY(-2px);
    text-shadow: none !important;
    font-weight: 700 !important;
}

.nav-tabs .nav-link.active::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1), transparent);
}

/* === Tab Content === */
.tab-content {
    padding: var(--spacing-md) 0;
}

/* === Statistics Cards === */
.stats-card {
    text-align: center;
    transition: all 0.3s ease;
    border-radius: 16px !important;
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
    padding: 1.5rem !important;
    position: relative;
    overflow: hidden;
}

.stats-card:hover {
    transform: translateY(-6px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12) !important;
}

.stats-card.border-primary {
    border: 2px solid #667eea !important;
    background: rgba(255, 255, 255, 0.98) !important;
}

.stats-card.border-warning {
    border: 2px solid #f6ad55 !important;
    background: rgba(255, 255, 255, 0.98) !important;
}

.stats-card.border-success {
    border: 2px solid #48bb78 !important;
    background: rgba(255, 255, 255, 0.98) !important;
}

.stats-card.border-info {
    border: 2px solid #4299e1 !important;
    background: rgba(255, 255, 255, 0.98) !important;
}

.stats-icon {
    font-size: 2.5rem !important;
    margin-bottom: 0.75rem !important;
    opacity: 0.9;
}

.stats-number {
    font-size: 2.5rem !important;
    font-weight: 800 !important;
    margin-bottom: 0.5rem !important;
    color: #2d3748 !important;
    text-shadow: none !important;
    line-height: 1.2;
}

.stats-label {
    font-size: 0.9rem !important;
    color: #64748b !important;
    font-weight: 600 !important;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    text-shadow: none !important;
}

/* === Enhanced Form Styling === */
.form-label {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    font-size: 0.95rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-label i {
    color: #6A1B9A;
    font-size: 1rem;
}

.form-control {
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    padding: 0.875rem 1.125rem;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    background: #ffffff;
    color: #2d3748;
    width: 100%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
    height: auto;
    min-height: calc(1.5em + 0.875rem + 0.875rem + 4px);
}

.form-control:focus {
    border-color: #6A1B9A;
    box-shadow: 0 0 0 3px rgba(106, 27, 154, 0.1), 0 4px 12px rgba(0, 0, 0, 0.05);
    background: #ffffff;
    color: #2d3748;
    outline: none;
    transform: translateY(-1px);
}

.form-control::placeholder {
    color: #a0aec0;
    opacity: 1;
    font-weight: 400;
}

/* Professional form container */
#addParcelForm {
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%) !important;
    border: 1px solid rgba(106, 27, 154, 0.1) !important;
    border-radius: 20px !important;
    padding: 2.5rem !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.06) !important;
    position: relative;
    overflow: hidden;
}

#addParcelForm::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #6A1B9A 0%, #FF9800 100%);
    border-radius: 20px 20px 0 0;
}

/* Form text styling */
.form-text {
    color: #718096;
    font-size: 0.85rem;
    margin-top: 0.375rem;
    font-weight: 400;
}

/* === Custom Weight Input (No Bootstrap Input Group) === */
.custom-weight-input {
    position: relative;
    display: flex;
    align-items: stretch;
    width: 100%;
    background: #ffffff;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
    transition: all 0.3s ease;
    overflow: hidden;
    min-height: calc(1.5em + 0.875rem + 0.875rem + 4px);
}

.custom-weight-input:hover {
    border-color: #cbd5e0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.custom-weight-input:focus-within {
    border-color: #6A1B9A;
    box-shadow: 0 0 0 3px rgba(106, 27, 154, 0.1), 0 4px 12px rgba(0, 0, 0, 0.05);
    transform: translateY(-1px);
}

.weight-input {
    flex: 1;
    border: none !important;
    background: transparent !important;
    padding: 0.875rem 1.125rem !important;
    font-size: 0.95rem !important;
    color: #2d3748 !important;
    border-radius: 0 !important;
    box-shadow: none !important;
    outline: none !important;
    margin: 0 !important;
}

.weight-input:focus {
    border: none !important;
    background: transparent !important;
    box-shadow: none !important;
    outline: none !important;
}

.weight-input::placeholder {
    color: #a0aec0;
    opacity: 1;
    font-weight: 400;
}

.weight-unit {
    display: flex;
    align-items: center;
    padding: 0.875rem 1rem;
    font-size: 0.95rem;
    font-weight: 600;
    color: #6A1B9A;
    background: #f7fafc;
    border-left: 1px solid #e2e8f0;
    white-space: nowrap;
    min-width: 50px;
    justify-content: center;
}

/* Enhanced input focus states */
.form-control:hover:not(:focus) {
    border-color: #cbd5e0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

/* === Enhanced Button Styling === */
.btn-primary {
    background: linear-gradient(135deg, #6A1B9A 0%, #FF9800 100%);
    border: none;
    padding: 0.875rem 2rem;
    font-weight: 600;
    border-radius: 12px;
    transition: all 0.3s ease;
    color: white;
    font-size: 0.95rem;
    box-shadow: 0 4px 12px rgba(106, 27, 154, 0.2);
    position: relative;
    overflow: hidden;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-primary:hover::before {
    left: 100%;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(106, 27, 154, 0.4);
    background: linear-gradient(135deg, #5a1a7a 0%, #e6870a 100%);
    color: white;
}

.btn-secondary {
    background: #ffffff;
    border: 2px solid #e2e8f0;
    color: #4a5568;
    padding: 0.875rem 2rem;
    font-weight: 600;
    border-radius: 12px;
    transition: all 0.3s ease;
    font-size: 0.95rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
}

.btn-secondary:hover {
    background: #f7fafc;
    border-color: #cbd5e0;
    color: #2d3748;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

/* Button group spacing */
.d-flex.gap-2 {
    gap: 1rem !important;
    margin-top: 2rem;
}

/* === Table Styling === */
.table {
    border-radius: var(--radius-lg);
    overflow: hidden;
}

.table-light th {
    background: var(--bg-light);
    border: none;
    font-weight: var(--font-weight-semibold);
    color: var(--text-secondary);
    text-transform: uppercase;
    font-size: var(--font-size-xs);
    letter-spacing: 0.5px;
}

.table tbody tr {
    transition: var(--transition);
}

.table tbody tr:hover {
    background: rgba(102, 126, 234, 0.05);
    transform: translateY(-1px);
}

/* === Footer Specific === */
.footer-custom {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: var(--spacing-3xl) 0 var(--spacing-2xl) 0;
    margin-top: var(--spacing-4xl);
    border-top: 1px solid rgba(106, 27, 154, 0.2);
    position: relative;
    clear: both;
}

.footer-custom::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 3px !important;
    background: linear-gradient(135deg, #6A1B9A 0%, #FF9800 100%);
    margin: 0 !important;
    padding: 0 !important;
    border: 0 !important;
    z-index: 1;
}

.footer-logo {
    height: 48px;
    width: 48px;
    border-radius: var(--radius-lg);
    margin-right: var(--spacing-md);
}

.footer-title {
    color: var(--dark-color);
    font-weight: var(--font-weight-bold);
    margin: 0;
}

.footer-subtitle {
    color: var(--text-muted);
    margin: 0;
    font-size: var(--font-size-sm);
}

.footer-description {
    line-height: var(--line-height-relaxed);
    font-size: var(--font-size-sm);
    color: var(--text-muted);
}

.footer-section-title {
    color: var(--dark-color);
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-md);
}

.contact-info {
    color: var(--text-muted);
    font-size: var(--font-size-sm);
    line-height: var(--line-height-loose);
}

.contact-info i {
    color: #6A1B9A;
    width: 16px;
}



/* === Responsive Design === */
@media (max-width: 768px) {
    .dashboard-container {
        margin: var(--spacing-md);
        padding: var(--spacing-xl);
        border-radius: 16px;
    }

    .navbar-custom {
        padding: var(--spacing-md);
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .nav-tabs .nav-link {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: var(--font-size-sm);
    }
}

/* === Dropdown Menu Z-Index Fix === */
.dropdown-menu {
    z-index: 1050 !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15) !important;
    border: 1px solid rgba(0, 0, 0, 0.1) !important;
    border-radius: 12px !important;
    padding: 0.5rem 0 !important;
    background: rgba(255, 255, 255, 0.98) !important;
    backdrop-filter: blur(20px) !important;
}

.dropdown-item {
    padding: 0.75rem 1.25rem !important;
    transition: all 0.2s ease !important;
    border-radius: 0 !important;
    color: #4a5568 !important;
}

.dropdown-item:hover {
    background: linear-gradient(135deg, rgba(106, 27, 154, 0.1), rgba(255, 152, 0, 0.1)) !important;
    color: #6A1B9A !important;
    transform: translateX(4px) !important;
}

.dropdown-item i {
    color: #6A1B9A !important;
    width: 16px !important;
}

.dropdown-divider {
    margin: 0.5rem 0 !important;
    border-color: rgba(106, 27, 154, 0.2) !important;
}

/* User dropdown specific styling */
#userDropdown {
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    background: rgba(255, 255, 255, 0.1) !important;
    backdrop-filter: blur(10px) !important;
    color: white !important;
    transition: all 0.3s ease !important;
}

#userDropdown:hover {
    background: rgba(255, 255, 255, 0.2) !important;
    border-color: rgba(255, 255, 255, 0.5) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 15px rgba(255, 255, 255, 0.2) !important;
}

/* === CLEAN SPACING === */
/* Simple and clean - no nuclear options needed */

/* === Enhanced Modal Styling === */
.modal {
    z-index: 1055 !important;
}

.modal-backdrop {
    z-index: 1050 !important;
}

.modal-content {
    border-radius: 20px !important;
    border: none !important;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15) !important;
    z-index: 1056 !important;
    position: relative !important;
    background-color: white !important;
    opacity: 1 !important;
    transform: none !important;
}

.modal.show .modal-dialog {
    transform: none !important;
}

.modal-dialog {
    margin: 1.75rem auto !important;
    max-width: 800px !important;
}

/* Prevent unwanted modal backdrops */
.modal-backdrop:not(.show) {
    display: none !important;
}

/* Ensure SweetAlert doesn't create modal backdrops */
.swal2-container {
    z-index: 9999 !important;
}

.swal2-toast {
    z-index: 10000 !important;
}

.modal-header {
    background: linear-gradient(135deg, #6A1B9A 0%, #FF9800 100%) !important;
    color: white !important;
    border-radius: 20px 20px 0 0 !important;
    border-bottom: none !important;
    padding: 1.5rem 2rem !important;
}

.modal-title {
    font-weight: 700 !important;
    font-size: 1.25rem !important;
}

.btn-close {
    filter: brightness(0) invert(1) !important;
    opacity: 0.8 !important;
}

.btn-close:hover {
    opacity: 1 !important;
}

.modal-body {
    padding: 2rem !important;
}

.form-label.fw-bold {
    color: #6A1B9A !important;
    font-size: 0.9rem !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    margin-bottom: 0.5rem !important;
}

.form-control-plaintext {
    background: #f8f9fa !important;
    border: 1px solid #e9ecef !important;
    border-radius: 8px !important;
    padding: 0.75rem 1rem !important;
    margin-bottom: 0 !important;
    font-weight: 500 !important;
    color: #2d3748 !important;
}

#viewQRCode {
    background: white !important;
    padding: 1rem !important;
    border-radius: 12px !important;
    border: 2px solid #e9ecef !important;
    display: inline-block !important;
}

.modal-footer {
    border-top: 1px solid #e9ecef !important;
    padding: 1.5rem 2rem !important;
    border-radius: 0 0 20px 20px !important;
}

/* === Enhanced Action Buttons === */
.btn-group-sm .btn {
    padding: 0.5rem 0.75rem !important;
    font-size: 0.875rem !important;
    border-radius: 8px !important;
    margin: 0 2px !important;
    transition: all 0.3s ease !important;
    border: 2px solid transparent !important;
    font-weight: 600 !important;
}

.btn-outline-primary {
    color: #6A1B9A !important;
    border-color: #6A1B9A !important;
    background: rgba(106, 27, 154, 0.05) !important;
}

.btn-outline-primary:hover {
    background: #6A1B9A !important;
    border-color: #6A1B9A !important;
    color: white !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(106, 27, 154, 0.3) !important;
}

.btn-outline-warning {
    color: #f6ad55 !important;
    border-color: #f6ad55 !important;
    background: rgba(246, 173, 85, 0.05) !important;
}

.btn-outline-warning:hover {
    background: #f6ad55 !important;
    border-color: #f6ad55 !important;
    color: white !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(246, 173, 85, 0.3) !important;
}

.btn-outline-info {
    color: #4299e1 !important;
    border-color: #4299e1 !important;
    background: rgba(66, 153, 225, 0.05) !important;
}

.btn-outline-info:hover {
    background: #4299e1 !important;
    border-color: #4299e1 !important;
    color: white !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(66, 153, 225, 0.3) !important;
}

.btn-outline-danger {
    color: #e53e3e !important;
    border-color: #e53e3e !important;
    background: rgba(229, 62, 62, 0.05) !important;
}

.btn-outline-danger:hover {
    background: #e53e3e !important;
    border-color: #e53e3e !important;
    color: white !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(229, 62, 62, 0.3) !important;
}

/* === Enhanced Table Styling === */
.table tbody tr:hover .btn-group .btn {
    opacity: 1 !important;
    visibility: visible !important;
}

.btn-group .btn {
    opacity: 0.7;
    transition: all 0.3s ease;
}

.btn-group:hover .btn {
    opacity: 1;
}

/* === QR Code Container Styling === */
#viewQRCode {
    min-height: 150px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%) !important;
    border: 2px dashed #e9ecef !important;
    border-radius: 12px !important;
    padding: 1rem !important;
    transition: all 0.3s ease !important;
}

#viewQRCode:has(canvas) {
    border-style: solid !important;
    border-color: #6A1B9A !important;
    background: white !important;
}

/* === Loading Animation === */
.fa-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* === Enhanced QR Generation Styling === */
#parcelInfoCard {
    transition: all 0.3s ease;
    border: 2px solid #e2e8f0 !important;
}

#parcelInfoCard .card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
    border-bottom: 1px solid #dee2e6 !important;
}

#qrCodeContainer .card {
    transition: all 0.3s ease;
    box-shadow: 0 8px 32px rgba(106, 27, 154, 0.15) !important;
}

#qrCodeContainer .card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(106, 27, 154, 0.2) !important;
}

#qrCodeDisplay {
    transition: all 0.3s ease;
    min-height: 250px;
    display: flex !important;
    align-items: center;
    justify-content: center;
}

#qrCodeDisplay canvas {
    border-radius: 8px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

/* QR Action Buttons */
#qrCodeContainer .btn {
    font-weight: 600 !important;
    padding: 0.75rem 1.5rem !important;
    transition: all 0.3s ease !important;
    border: none !important;
    position: relative;
    overflow: hidden;
}

#qrCodeContainer .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

#qrCodeContainer .btn:hover::before {
    left: 100%;
}

#qrCodeContainer .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15) !important;
}

#qrCodeContainer .btn-success {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%) !important;
}

#qrCodeContainer .btn-info {
    background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%) !important;
}

#qrCodeContainer .btn-warning {
    background: linear-gradient(135deg, #f6ad55 0%, #ed8936 100%) !important;
    color: white !important;
}

/* Parcel Info Styling */
.card-body .row .col-6 {
    margin-bottom: 0.75rem;
}

.card-body .row .col-6 small {
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 600;
}

.card-body .row .col-6 div {
    font-size: 0.95rem;
    margin-top: 0.25rem;
}

/* Enhanced Select Styling */
#qrTrackingNumber {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e") !important;
    background-position: right 0.75rem center !important;
    background-repeat: no-repeat !important;
    background-size: 1.5em 1.5em !important;
    padding-right: 2.5rem !important;
}

#qrTrackingNumber:focus {
    border-color: #6A1B9A !important;
    box-shadow: 0 0 0 3px rgba(106, 27, 154, 0.1) !important;
    transform: translateY(-1px);
}

/* Loading Animation for QR Generation */
.qr-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    color: #6A1B9A;
}

.qr-loading i {
    font-size: 2rem;
    margin-bottom: 1rem;
    animation: spin 1s linear infinite;
}

/* Success State Animation */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

#qrCodeContainer.show {
    animation: fadeInUp 0.5s ease-out;
}

/* Mobile Responsiveness for QR Section */
@media (max-width: 768px) {
    #qrCodeContainer .card {
        margin-top: 2rem;
    }

    #qrCodeDisplay {
        min-height: 200px;
    }

    #qrCodeContainer .btn {
        padding: 0.625rem 1.25rem !important;
        font-size: 0.875rem !important;
    }

    #parcelInfoCard .row .col-6 {
        margin-bottom: 1rem;
    }
}
/* ===================================
   PPMS Receiver Dashboard Styles
   ================================== */

/* === Global Styles === */
body {
    background: linear-gradient(135deg, #667eea 0%, #43e97b 100%);
    background-attachment: fixed;
    min-height: 100vh;
}

/* === Navbar Specific === */
.navbar-custom {
    background: var(--dark-gradient);
    color: var(--text-white);
    padding: var(--spacing-xl) var(--spacing-2xl);
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: var(--shadow-lg);
    position: relative;
    overflow: hidden;
}

.navbar-custom::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--primary-gradient);
}

.navbar-brand {
    font-weight: var(--font-weight-bold);
    font-size: var(--font-size-lg);
    letter-spacing: 0.5px;
}

.navbar-welcome {
    font-weight: var(--font-weight-medium);
    opacity: var(--opacity-hover);
}

/* === Dashboard Container === */
.dashboard-container {
    max-width: var(--container-max-width);
    margin: var(--spacing-2xl) auto;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
    padding: var(--spacing-2xl) var(--spacing-3xl);
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: var(--transition);
}

.dashboard-container:hover {
    box-shadow: 0 15px 50px rgba(0, 0, 0, 0.12);
}

/* === Welcome Section === */
.welcome-section {
    background: linear-gradient(135deg, #43e97b 0%, #38d9a9 100%);
    border-radius: var(--border-radius);
    color: var(--text-white);
    position: relative;
    overflow: hidden;
    padding: var(--spacing-2xl);
    margin-bottom: var(--spacing-3xl);
    box-shadow: 0 10px 30px rgba(67, 233, 123, 0.4);
}

.welcome-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1), transparent);
}

.welcome-avatar {
    width: 60px;
    height: 60px;
    background: rgba(255,255,255,0.2);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    position: relative;
    z-index: 1;
}

.welcome-content {
    position: relative;
    z-index: 1;
}

.welcome-title {
    margin: 0;
    font-weight: var(--font-weight-bold);
    font-size: var(--font-size-3xl);
}

.welcome-subtitle {
    margin: 0;
    opacity: var(--opacity-hover);
    font-size: var(--font-size-lg);
}

.welcome-icon {
    font-size: var(--font-size-4xl);
    opacity: 0.3;
    position: relative;
    z-index: 1;
}

/* === Modern Tabs === */
.nav-tabs {
    border: none;
    margin-bottom: var(--spacing-2xl);
    background: var(--bg-light);
    border-radius: var(--border-radius);
    padding: var(--spacing-sm);
}

.nav-tabs .nav-link {
    color: var(--text-muted);
    border: none;
    border-radius: var(--radius-lg);
    margin-right: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-xl);
    font-weight: var(--font-weight-semibold);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.nav-tabs .nav-link:hover:not(.active) {
    background: rgba(67, 233, 123, 0.15);
    color: #43e97b;
    transform: translateY(-1px);
}

.nav-tabs .nav-link.active {
    background: linear-gradient(135deg, #43e97b 0%, #38d9a9 100%);
    color: var(--text-white);
    box-shadow: 0 4px 15px rgba(67, 233, 123, 0.5);
    transform: translateY(-2px);
}

.nav-tabs .nav-link.active::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1), transparent);
}

/* === Tab Content === */
.tab-content {
    padding: var(--spacing-md) 0;
}

/* === Statistics Cards === */
.stats-card {
    text-align: center;
    transition: var(--transition);
}

.stats-card:hover {
    transform: translateY(-4px);
}

.stats-card.border-primary {
    border: 2px solid #43e97b !important;
    background: linear-gradient(135deg, rgba(67, 233, 123, 0.08), rgba(56, 217, 169, 0.08));
}

.stats-card.border-warning {
    border: 2px solid #38d9a9 !important;
    background: linear-gradient(135deg, rgba(56, 217, 169, 0.08), rgba(67, 233, 123, 0.08));
}

.stats-card.border-success {
    border: 2px solid #43e97b !important;
    background: linear-gradient(135deg, rgba(67, 233, 123, 0.08), rgba(56, 217, 169, 0.08));
}

.stats-icon {
    font-size: var(--font-size-2xl);
    margin-bottom: var(--spacing-sm);
}

.stats-number {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    margin-bottom: 0;
}

.stats-label {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
}

/* === Detail Items === */
.detail-item {
    margin-bottom: var(--spacing-lg);
}

.detail-item label {
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-bold);
    text-transform: uppercase;
    letter-spacing: 1px;
    color: var(--text-muted);
    margin-bottom: var(--spacing-xs);
    display: block;
}

.detail-item .fw-medium {
    font-weight: var(--font-weight-semibold);
    color: var(--text-secondary);
    font-size: var(--font-size-base);
}

/* === Empty States === */
.empty-state {
    padding: var(--spacing-4xl) var(--spacing-2xl) !important;
    text-align: center;
}

.empty-state i {
    opacity: 0.6;
    margin-bottom: var(--spacing-xl);
}

.empty-state h5 {
    color: var(--text-muted);
    margin-bottom: var(--spacing-md);
}

.empty-state p {
    color: var(--text-muted);
    margin-bottom: var(--spacing-2xl);
}

/* === Footer Specific === */
.footer-custom {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: var(--spacing-3xl) 0 var(--spacing-2xl) 0;
    margin-top: var(--spacing-4xl);
    border-top: 1px solid rgba(102, 126, 234, 0.2);
    position: relative;
}

.footer-custom::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(135deg, #43e97b 0%, #38d9a9 100%);
}

.footer-logo {
    height: 48px;
    width: 48px;
    border-radius: var(--radius-lg);
    margin-right: var(--spacing-md);
}

.footer-title {
    color: var(--dark-color);
    font-weight: var(--font-weight-bold);
    margin: 0;
}

.footer-subtitle {
    color: var(--text-muted);
    margin: 0;
    font-size: var(--font-size-sm);
}

.footer-description {
    line-height: var(--line-height-relaxed);
    font-size: var(--font-size-sm);
    color: var(--text-muted);
}

.footer-section-title {
    color: var(--dark-color);
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-md);
}

.contact-info {
    color: var(--text-muted);
    font-size: var(--font-size-sm);
    line-height: var(--line-height-loose);
}

.contact-info i {
    color: var(--primary-color);
    width: 16px;
}

.delivery-partners {
    opacity: 0.9;
}

/* Temporary placeholder styling until real logos are added */
.delivery-partner-placeholder {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    height: 40px;
    padding: 8px 16px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.85rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    min-width: 80px;
    text-align: center;
}

.delivery-partner-placeholder:hover {
    background: rgba(255, 255, 255, 0.15);
    color: rgba(255, 255, 255, 1);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Original logo styling (for when real images are added) */
.delivery-partner-logo {
    height: 40px;
    filter: grayscale(70%) brightness(0.9);
    transition: all 0.3s ease;
    margin: 0 var(--spacing-sm);
    opacity: 0.8;
}

.delivery-partner-logo:hover {
    filter: grayscale(0%) brightness(1);
    transform: scale(1.1);
    opacity: 1;
}

/* === Form Styling === */
.form-label {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-sm);
    display: block;
}

.form-control {
    border: 2px solid var(--border-light);
    border-radius: var(--radius-lg);
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-sm);
    transition: var(--transition);
    background: #ffffff;
    color: var(--text-primary);
    width: 100%;
}

.form-control:focus {
    border-color: #43e97b;
    box-shadow: 0 0 0 3px rgba(67, 233, 123, 0.15);
    background: #ffffff;
    color: var(--text-primary);
    outline: none;
}

.form-control::placeholder {
    color: var(--text-light);
    opacity: 1;
}

/* Specific styling for tracking input */
#trackingNumber {
    background: #ffffff !important;
    color: var(--text-primary) !important;
    border: 2px solid var(--border-light) !important;
    font-size: var(--font-size-base) !important;
    padding: var(--spacing-md) !important;
}

#trackingNumber:focus {
    background: #ffffff !important;
    color: var(--text-primary) !important;
    border-color: #43e97b !important;
    box-shadow: 0 0 0 3px rgba(67, 233, 123, 0.15) !important;
}

/* Enhanced label styling for tracking */
label[for="trackingNumber"] {
    font-weight: 700 !important;
    color: var(--text-primary) !important;
    font-size: var(--font-size-base) !important;
    margin-bottom: var(--spacing-md) !important;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: flex;
    align-items: center;
}

label[for="trackingNumber"]::before {
    content: "📦";
    margin-right: var(--spacing-sm);
    font-size: var(--font-size-lg);
}

/* Tracking form container */
.tab-pane#tracking form {
    background: var(--bg-light);
    padding: var(--spacing-xl);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-light);
    margin-top: var(--spacing-lg);
}

/* Tracking button styling */
.tab-pane#tracking .btn-primary {
    background: linear-gradient(135deg, #43e97b 0%, #38d9a9 100%);
    border: none;
    padding: var(--spacing-sm) var(--spacing-xl);
    font-weight: 600;
    border-radius: var(--radius-lg);
    transition: var(--transition);
}

.tab-pane#tracking .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(67, 233, 123, 0.5);
    background: linear-gradient(135deg, #3dd46f 0%, #2dd4aa 100%);
}

/* Override any dark theme or conflicting styles */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
textarea,
select {
    background-color: #ffffff !important;
    color: var(--text-primary) !important;
    border: 2px solid var(--border-light) !important;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus,
input[type="number"]:focus,
textarea:focus,
select:focus {
    background-color: #ffffff !important;
    color: var(--text-primary) !important;
    border-color: #43e97b !important;
    box-shadow: 0 0 0 3px rgba(67, 233, 123, 0.15) !important;
}

/* === Responsive Design === */
@media (max-width: 768px) {
    .dashboard-container {
        margin: var(--spacing-md);
        padding: var(--spacing-xl);
    }
    
    .navbar-custom {
        padding: var(--spacing-md);
        flex-direction: column;
        gap: var(--spacing-md);
    }
    
    .nav-tabs .nav-link {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: var(--font-size-sm);
    }
    
    .welcome-section {
        padding: var(--spacing-xl);
    }
    
    .welcome-title {
        font-size: var(--font-size-2xl);
    }
    
    .welcome-subtitle {
        font-size: var(--font-size-base);
    }
}

/* === Modern Parcel Details Styling === */
.parcel-details-container {
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 24px;
    padding: 0;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(67, 233, 123, 0.1);
    overflow: hidden;
    position: relative;
}

.parcel-details-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

/* Header Section */
.parcel-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.tracking-badge {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.tracking-label {
    font-size: 0.85rem;
    opacity: 0.9;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: white !important;
}

.tracking-number {
    font-size: 1.5rem;
    font-weight: 700;
    font-family: 'Courier New', monospace;
    background: rgba(255, 255, 255, 0.25);
    color: white !important;
    padding: 0.5rem 1rem;
    border-radius: 12px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Status Badge */
.status-container {
    display: flex;
    align-items: center;
}

.status-badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border-radius: 50px;
    font-weight: 600;
    font-size: 0.95rem;
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.status-pending {
    background: rgba(255, 193, 7, 0.9);
    color: #856404;
}

.status-retrieved {
    background: rgba(40, 167, 69, 0.9);
    color: white;
}

.status-other {
    background: rgba(108, 117, 125, 0.9);
    color: white;
}

/* Details Grid */
.parcel-details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    padding: 2rem;
}

.detail-card {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.detail-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.detail-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.detail-card:hover::before {
    opacity: 1;
}

.detail-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.detail-content {
    flex: 1;
}

.detail-label {
    font-size: 0.8rem;
    font-weight: 600;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.5rem;
}

.detail-value {
    font-size: 1.1rem;
    font-weight: 600;
    color: #1f2937;
    line-height: 1.4;
}

.detail-subvalue {
    font-size: 0.9rem;
    font-weight: 400;
    color: #6b7280;
    margin-top: 0.25rem;
}

/* QR Code Card Special Styling */
.qr-card {
    grid-column: span 1;
}

.qr-code-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 12px;
    margin-top: 0.5rem;
    min-height: 120px;
}

/* Action Alert */
.action-alert {
    margin: 0 2rem 2rem 2rem;
    background: linear-gradient(135deg, #fef3cd 0%, #fff3cd 100%);
    border: 1px solid #ffeaa7;
    border-radius: 16px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.success-alert {
    background: linear-gradient(135deg, #d1f2eb 0%, #d4edda 100%);
    border-color: #c3e6cb;
}

.alert-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    background: #ffc107;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.success-alert .alert-icon {
    background: #28a745;
}

.alert-content {
    flex: 1;
}

.alert-title {
    font-size: 1.1rem;
    font-weight: 700;
    color: #856404;
    margin-bottom: 0.5rem;
}

.success-alert .alert-title {
    color: #155724;
}

.alert-message {
    font-size: 0.95rem;
    color: #856404;
    line-height: 1.5;
}

.success-alert .alert-message {
    color: #155724;
}

/* Mobile Responsive for Parcel Details */
@media (max-width: 768px) {
    .parcel-header {
        flex-direction: column;
        text-align: center;
        padding: 1.5rem;
    }

    .parcel-details-grid {
        grid-template-columns: 1fr;
        padding: 1.5rem;
        gap: 1rem;
    }

    .tracking-number {
        font-size: 1.2rem;
    }

    .action-alert {
        margin: 0 1.5rem 1.5rem 1.5rem;
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .detail-card {
        padding: 1rem;
    }

    .detail-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
}

/* === CLEAN MODERN CAROUSEL === */
.modern-carousel-container {
    position: relative;
    max-width: 100%;
    margin: 0 auto;
    padding: 2rem 0;
    background: linear-gradient(135deg, rgba(139, 69, 19, 0.03) 0%, rgba(34, 197, 94, 0.03) 100%);
    border-radius: 20px;
    overflow: hidden;
}

.carousel-viewport {
    overflow: hidden;
    padding: 0 3rem;
    margin: 0 auto;
}

.carousel-track {
    display: flex;
    gap: 1.5rem;
    transition: transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    padding: 1rem 0;
}

.partner-card {
    flex: 0 0 220px;
    background: #ffffff;
    border-radius: 14px;
    box-shadow: 0 6px 24px rgba(0, 0, 0, 0.06);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    border: 1px solid rgba(0, 0, 0, 0.04);
    overflow: hidden;
    cursor: pointer;
}

.partner-card:hover {
    transform: translateY(-6px) scale(1.02);
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.12);
    border-color: rgba(139, 69, 19, 0.1);
}

.partner-card-inner {
    padding: 1.5rem 1rem;
    text-align: center;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.partner-logo-container {
    width: 60px;
    height: 60px;
    margin: 0 auto 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, rgba(139, 69, 19, 0.05) 0%, rgba(34, 197, 94, 0.05) 100%);
    border-radius: 12px;
    transition: all 0.3s ease;
}

.partner-card:hover .partner-logo-container {
    background: linear-gradient(135deg, rgba(139, 69, 19, 0.1) 0%, rgba(34, 197, 94, 0.1) 100%);
    transform: scale(1.05);
}

.partner-logo {
    max-width: 45px;
    max-height: 45px;
    object-fit: contain;
    filter: grayscale(30%) brightness(0.95);
    transition: all 0.3s ease;
}

.partner-card:hover .partner-logo {
    filter: grayscale(0%) brightness(1.1);
    transform: scale(1.1);
}

.partner-logo-placeholder {
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, rgba(139, 69, 19, 0.1) 0%, rgba(34, 197, 94, 0.1) 100%);
    border-radius: 10px;
    font-size: 0.65rem;
    font-weight: 600;
    color: rgba(139, 69, 19, 0.8);
    text-align: center;
    line-height: 1.2;
}

.partner-info {
    text-align: center;
}

.partner-name {
    font-size: 1.1rem;
    font-weight: 800;
    color: #1a202c;
    margin: 0;
    letter-spacing: -0.02em;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    text-align: center;
    line-height: 1.2;
}

/* Aesthetic Navigation Arrows */
.carousel-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 42px;
    height: 42px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
    z-index: 10;
    opacity: 0;
    transform: translateY(-50%) scale(0.9);
    color: white;
}

.modern-carousel-container:hover .carousel-nav {
    opacity: 1;
    transform: translateY(-50%) scale(1);
}

.carousel-nav-prev {
    left: 1rem;
}

.carousel-nav-next {
    right: 1rem;
}

.carousel-nav:hover {
    background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 6px 24px rgba(118, 75, 162, 0.4);
}

.carousel-nav i {
    font-size: 0.9rem;
    transition: transform 0.2s ease;
}

.carousel-nav:hover i {
    transform: scale(1.1);
}

/* Aesthetic Indicators */
.carousel-indicators {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 1.5rem;
    padding: 0.75rem 1.25rem;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 25px;
    width: fit-content;
    margin-left: auto;
    margin-right: auto;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
    backdrop-filter: blur(10px);
}

.carousel-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    border: none;
    background: rgba(139, 69, 19, 0.25);
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    overflow: hidden;
}

.carousel-indicator::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    transform: scale(0);
    transition: transform 0.3s ease;
}

.carousel-indicator.active::before,
.carousel-indicator:hover::before {
    transform: scale(1);
}

.carousel-indicator.active {
    background: rgba(255, 255, 255, 0.9);
    transform: scale(1.4);
    box-shadow: 0 2px 8px rgba(139, 69, 19, 0.2);
}

.carousel-indicator:hover {
    transform: scale(1.2);
    background: rgba(255, 255, 255, 0.7);
}

/* Carousel Responsive Design */
@media (max-width: 1024px) {
    .partner-card {
        flex: 0 0 200px;
    }

    .carousel-viewport {
        padding: 0 2rem;
    }
}

@media (max-width: 768px) {
    .modern-carousel-container {
        padding: 1.5rem 0;
    }

    .carousel-viewport {
        padding: 0 1rem;
    }

    .partner-card {
        flex: 0 0 180px;
    }

    .partner-card-inner {
        padding: 1.25rem 0.75rem;
    }

    .partner-logo-container {
        width: 50px;
        height: 50px;
        margin-bottom: 0.75rem;
    }

    .partner-logo {
        max-width: 38px;
        max-height: 38px;
    }

    .partner-logo-placeholder {
        width: 38px;
        height: 38px;
        font-size: 0.6rem;
    }

    .partner-name {
        font-size: 1rem;
    }

    .carousel-nav {
        width: 36px;
        height: 36px;
    }

    .carousel-nav i {
        font-size: 0.8rem;
    }

    .carousel-indicator {
        width: 6px;
        height: 6px;
    }
}

@media (max-width: 480px) {
    .partner-card {
        flex: 0 0 150px;
    }

    .carousel-track {
        gap: 1rem;
    }

    .partner-card-inner {
        padding: 1rem 0.5rem;
    }

    .partner-logo-container {
        width: 45px;
        height: 45px;
    }

    .partner-logo {
        max-width: 32px;
        max-height: 32px;
    }

    .partner-name {
        font-size: 0.95rem;
    }
}

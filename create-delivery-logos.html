<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Delivery Partner Logos</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .logo-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin: 20px 0;
        }
        .logo-box {
            width: 120px;
            height: 40px;
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 12px;
            color: #333;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .pos-malaysia { background: linear-gradient(135deg, #1e40af, #3b82f6); color: white; }
        .gdex { background: linear-gradient(135deg, #dc2626, #ef4444); color: white; }
        .flash { background: linear-gradient(135deg, #f59e0b, #fbbf24); color: white; }
        .shopee { background: linear-gradient(135deg, #ea580c, #f97316); color: white; }
        .jnt { background: linear-gradient(135deg, #dc2626, #b91c1c); color: white; }
        .dhl { background: linear-gradient(135deg, #facc15, #eab308); color: #1f2937; }
        .fedex { background: linear-gradient(135deg, #7c3aed, #8b5cf6); color: white; }
        .ninja { background: linear-gradient(135deg, #1f2937, #374151); color: white; }
        
        .instructions {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
        }
        
        button:hover {
            background: #2563eb;
        }
    </style>
</head>
<body>
    <h1>🚚 Delivery Partner Logo Generator</h1>
    
    <div class="instructions">
        <h3>📋 Instructions:</h3>
        <p>1. Right-click on each logo below and select "Save image as..."</p>
        <p>2. Save them in your <code>assets/</code> folder with these exact names:</p>
        <ul>
            <li>posMalaysia.png</li>
            <li>gdex.png</li>
            <li>flashexpress.png</li>
            <li>shopeeExpress.png</li>
            <li>JNT.png</li>
            <li>dhl.png</li>
            <li>fedex.png</li>
            <li>ninjavan.png</li>
        </ul>
        <p>3. Refresh your dashboard to see the logos!</p>
    </div>

    <div class="logo-container">
        <canvas id="pos-malaysia" width="120" height="40" class="logo-box pos-malaysia"></canvas>
        <canvas id="gdex" width="120" height="40" class="logo-box gdex"></canvas>
        <canvas id="flash" width="120" height="40" class="logo-box flash"></canvas>
        <canvas id="shopee" width="120" height="40" class="logo-box shopee"></canvas>
        <canvas id="jnt" width="120" height="40" class="logo-box jnt"></canvas>
        <canvas id="dhl" width="120" height="40" class="logo-box dhl"></canvas>
        <canvas id="fedex" width="120" height="40" class="logo-box fedex"></canvas>
        <canvas id="ninja" width="120" height="40" class="logo-box ninja"></canvas>
    </div>

    <div class="instructions">
        <h3>🎨 Alternative: Use Text Placeholders</h3>
        <p>The current setup shows professional text placeholders that look good until you get real logos.</p>
        <button onclick="downloadAll()">📥 Download All Logos</button>
        <button onclick="window.open('https://www.pos.com.my/', '_blank')">🔗 Get Real Pos Malaysia Logo</button>
        <button onclick="window.open('https://www.gdexpress.com/', '_blank')">🔗 Get Real GDEX Logo</button>
    </div>

    <script>
        // Create canvas logos
        const logos = [
            { id: 'pos-malaysia', text: 'Pos Malaysia', gradient: ['#1e40af', '#3b82f6'] },
            { id: 'gdex', text: 'GDEX', gradient: ['#dc2626', '#ef4444'] },
            { id: 'flash', text: 'Flash Express', gradient: ['#f59e0b', '#fbbf24'] },
            { id: 'shopee', text: 'Shopee Express', gradient: ['#ea580c', '#f97316'] },
            { id: 'jnt', text: 'J&T Express', gradient: ['#dc2626', '#b91c1c'] },
            { id: 'dhl', text: 'DHL', gradient: ['#facc15', '#eab308'] },
            { id: 'fedex', text: 'FedEx', gradient: ['#7c3aed', '#8b5cf6'] },
            { id: 'ninja', text: 'Ninja Van', gradient: ['#1f2937', '#374151'] }
        ];

        logos.forEach(logo => {
            const canvas = document.getElementById(logo.id);
            const ctx = canvas.getContext('2d');
            
            // Create gradient
            const gradient = ctx.createLinearGradient(0, 0, 120, 40);
            gradient.addColorStop(0, logo.gradient[0]);
            gradient.addColorStop(1, logo.gradient[1]);
            
            // Fill background
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, 120, 40);
            
            // Add text
            ctx.fillStyle = logo.id === 'dhl' ? '#1f2937' : 'white';
            ctx.font = 'bold 11px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(logo.text, 60, 20);
        });

        function downloadAll() {
            logos.forEach(logo => {
                const canvas = document.getElementById(logo.id);
                const link = document.createElement('a');
                link.download = logo.id.replace('-', '') + '.png';
                link.href = canvas.toDataURL();
                link.click();
            });
        }
    </script>
</body>
</html>

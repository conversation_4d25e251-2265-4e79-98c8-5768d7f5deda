/* ===================================
   PPMS Password Validation Styles
   Strong password requirements styling
   ================================== */

/* === Popup-Style Password Requirements === */
.password-field-container {
    position: relative;
}

.password-requirements {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 1000;
    background: #ffffff;
    border: 1px solid #d1d5db;
    border-radius: 12px;
    padding: 12px;
    margin-top: 4px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-8px);
    transition: all 0.2s ease;
    pointer-events: none;
    font-size: 0.8rem;
}

.password-requirements.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
    pointer-events: auto;
}

.password-requirements::before {
    content: '';
    position: absolute;
    top: -6px;
    left: 20px;
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-bottom: 6px solid #d1d5db;
}

.password-requirements::after {
    content: '';
    position: absolute;
    top: -5px;
    left: 21px;
    width: 0;
    height: 0;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-bottom: 5px solid #ffffff;
}

.password-requirements small {
    font-weight: 600 !important;
    color: #1f2937 !important;
    font-size: 0.75rem !important;
    display: block !important;
    margin-bottom: 8px !important;
    position: relative;
}

.password-requirements-close {
    position: absolute;
    top: 4px;
    right: 4px;
    width: 20px;
    height: 20px;
    border: none;
    background: #6b7280;
    color: white;
    border-radius: 50%;
    font-size: 10px;
    cursor: pointer;
    display: none;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.password-requirements-close:hover {
    background: #374151;
    transform: scale(1.1);
}

.password-checklist {
    list-style: none;
    padding: 0;
    margin: 0;
    font-size: 0.75rem;
}

.password-checklist li {
    display: flex !important;
    align-items: center !important;
    padding: 3px 0 !important;
    transition: all 0.2s ease;
    font-weight: 500 !important;
    color: #374151 !important;
}

.password-checklist li.valid {
    color: #059669 !important;
}

.password-checklist li.valid .requirement-icon {
    background: #10b981;
    color: white;
    border-color: #10b981;
}

.password-checklist li.invalid {
    color: #374151 !important;
}

.password-checklist li.invalid .requirement-icon {
    background: transparent;
    color: #ef4444;
    border-color: #ef4444;
}

.requirement-icon {
    width: 14px;
    height: 14px;
    border: 1px solid #ef4444;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 8px;
    font-size: 8px;
    font-weight: bold;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.requirement-icon.valid::before {
    content: "✓";
}

.requirement-icon.invalid::before {
    content: "○";
    font-size: 8px;
}

/* === Sleek Password Strength Meter === */
.password-strength-meter {
    margin-top: 8px;
    padding-top: 8px;
    border-top: 1px solid #e5e7eb;
}

.strength-bar {
    width: 100%;
    height: 3px;
    background: #e5e7eb;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 4px;
    position: relative;
}

.strength-fill {
    height: 100%;
    width: 0%;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 8px;
    position: relative;
}

.strength-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.3) 50%, transparent 100%);
    animation: shimmer 2s infinite;
}

.strength-fill.very-weak {
    width: 20%;
    background: linear-gradient(90deg, #ef4444 0%, #dc2626 100%);
}

.strength-fill.weak {
    width: 40%;
    background: linear-gradient(90deg, #f59e0b 0%, #d97706 100%);
}

.strength-fill.medium {
    width: 60%;
    background: linear-gradient(90deg, #eab308 0%, #ca8a04 100%);
}

.strength-fill.strong {
    width: 80%;
    background: linear-gradient(90deg, #22c55e 0%, #16a34a 100%);
}

.strength-fill.very-strong {
    width: 100%;
    background: linear-gradient(90deg, #10b981 0%, #059669 100%);
}

.strength-text {
    font-size: 0.7rem;
    font-weight: 500;
    color: #374151;
    text-align: center;
}

.strength-text.very-weak {
    color: #ef4444;
}

.strength-text.weak {
    color: #f59e0b;
}

.strength-text.medium {
    color: #eab308;
}

.strength-text.strong {
    color: #22c55e;
}

.strength-text.very-strong {
    color: #10b981;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* === Sleek Password Match Indicator === */
.password-match-indicator {
    margin-top: 12px;
    padding: 10px 16px;
    border-radius: 10px;
    font-size: 0.8rem;
    font-weight: 600;
    display: none;
    text-align: center;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.password-match-indicator.match {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.05) 100%);
    color: #059669;
    border: 1px solid rgba(16, 185, 129, 0.3);
    display: block;
    box-shadow: 0 2px 10px rgba(16, 185, 129, 0.1);
}

.password-match-indicator.no-match {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(220, 38, 38, 0.05) 100%);
    color: #dc2626;
    border: 1px solid rgba(239, 68, 68, 0.3);
    display: block;
    box-shadow: 0 2px 10px rgba(239, 68, 68, 0.1);
}

/* === Form Field Enhancements === */
.password-field-container {
    position: relative;
}

.password-field-container .form-control {
    padding-right: 45px;
}

.password-toggle {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 4px;
    font-size: 0.875rem;
    transition: color 0.2s ease;
}

.password-toggle:hover {
    color: #374151;
}

.password-toggle:focus {
    outline: none;
    color: #667eea;
}

/* === Validation States === */
.form-control.is-valid,
.receiver-form-control.is-valid {
    border-color: #10b981 !important;
    box-shadow: 0 0 0 0.2rem rgba(16, 185, 129, 0.25) !important;
    /* Validation icons removed for cleaner look */
}

.form-control.is-invalid,
.receiver-form-control.is-invalid {
    border-color: #ef4444 !important;
    box-shadow: 0 0 0 0.2rem rgba(239, 68, 68, 0.25) !important;
    /* Validation icons removed for cleaner look */
}

.valid-feedback {
    color: #10b981;
    font-size: 0.875rem;
    margin-top: 4px;
}

.invalid-feedback {
    color: #ef4444;
    font-size: 0.875rem;
    margin-top: 4px;
}

/* === Responsive Design === */
@media (max-width: 768px) {
    .password-requirements {
        position: fixed;
        top: 50%;
        left: 50%;
        right: auto;
        transform: translate(-50%, -50%);
        width: 90%;
        max-width: 400px;
        z-index: 1050;
        margin-top: 0;
    }

    .password-requirements::before,
    .password-requirements::after {
        display: none;
    }

    .password-requirements-close {
        display: flex;
    }

    .password-checklist li {
        padding: 8px 0;
        font-size: 0.8rem;
    }

    .requirement-icon {
        width: 16px;
        height: 16px;
        margin-right: 8px;
    }

    .strength-bar {
        height: 3px;
    }

    .strength-text {
        font-size: 0.75rem;
    }

    .password-match-indicator {
        padding: 8px 12px;
        font-size: 0.75rem;
    }
}

/* === Mobile Overlay === */
@media (max-width: 768px) {
    .password-requirements.show::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.3);
        z-index: -1;
    }
}

/* === Animation for Requirements === */
@keyframes checkmarkBounce {
    0% { transform: scale(0.8); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.password-checklist li.valid i {
    animation: checkmarkBounce 0.3s ease;
}

/* === Focus States === */
.password-field-container:focus-within .password-requirements {
    border-color: #667eea;
    background: #ffffff;
}

/* === Accessibility === */
@media (prefers-reduced-motion: reduce) {
    .password-checklist li,
    .strength-fill,
    .password-toggle {
        transition: none;
    }
    
    .password-checklist li.valid i {
        animation: none;
    }
}

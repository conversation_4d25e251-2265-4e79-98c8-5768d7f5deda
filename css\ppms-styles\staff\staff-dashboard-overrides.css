/* ===================================
   PPMS Staff Dashboard Overrides
   Critical fixes and enhancements
   ================================== */

/* === Universal Reset for Perfect Alignment === */
* {
    box-sizing: border-box;
}

html, body {
    margin: 0 !important;
    padding: 0 !important;
    border: 0 !important;
    outline: 0 !important;
    vertical-align: baseline !important;
}

/* === Selective Reset - Only Target Spacing Pseudo-elements, Not Icons === */
body::before, body::after {
    content: none !important;
    display: none !important;
    height: 0 !important;
    width: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* === Remove Default Browser Spacing === */
body {
    line-height: 1 !important;
    -webkit-margin-before: 0 !important;
    -webkit-margin-after: 0 !important;
    -webkit-margin-start: 0 !important;
    -webkit-margin-end: 0 !important;
    -webkit-padding-before: 0 !important;
    -webkit-padding-after: 0 !important;
    -webkit-padding-start: 0 !important;
    -webkit-padding-end: 0 !important;
}

/* === Staff Theme Colors === */
.welcome-section {
    background: linear-gradient(135deg, #6A1B9A 0%, #FF9800 100%) !important;
    box-shadow: 0 10px 30px rgba(106, 27, 154, 0.4) !important;
}

.nav-tabs .nav-link.active {
    background: linear-gradient(135deg, #6A1B9A 0%, #FF9800 100%) !important;
    box-shadow: 0 4px 15px rgba(106, 27, 154, 0.5) !important;
}

.nav-tabs .nav-link:hover:not(.active) {
    background: rgba(106, 27, 154, 0.15) !important;
    color: #6A1B9A !important;
}

/* === Enhanced Dashboard Container === */
.dashboard-container {
    background: rgba(255, 255, 255, 0.98) !important;
    backdrop-filter: blur(20px) !important;
    border-radius: 20px !important;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    margin: 2rem auto !important;
    padding: 2.5rem !important;
    max-width: 1200px !important;
    position: relative !important;
    z-index: 1 !important;
}

/* === Professional Card Styling === */
.card {
    border: none !important;
    border-radius: 16px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06) !important;
    transition: all 0.3s ease !important;
}

.card:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12) !important;
}

/* === Professional Tab Styling === */
.nav-tabs {
    background: rgba(255, 255, 255, 0.8) !important;
    backdrop-filter: blur(10px) !important;
    border-radius: 16px !important;
    padding: 8px !important;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04) !important;
}

.nav-tabs .nav-link {
    border-radius: 12px !important;
    font-weight: 600 !important;
    font-size: 0.95rem !important;
    padding: 12px 24px !important;
    transition: all 0.3s ease !important;
    color: #64748b !important;
}

.nav-tabs .nav-link:hover:not(.active) {
    background: rgba(106, 27, 154, 0.08) !important;
    color: #6A1B9A !important;
    transform: translateY(-1px) !important;
}

/* === Enhanced Buttons === */
.btn {
    border-radius: 12px !important;
    font-weight: 600 !important;
    padding: 12px 24px !important;
    transition: all 0.3s ease !important;
    border: none !important;
}

.btn:hover {
    transform: translateY(-2px) !important;
}

/* === Professional Badges === */
.badge {
    border-radius: 8px !important;
    font-weight: 600 !important;
    padding: 6px 12px !important;
    font-size: 0.8rem !important;
}

/* === Enhanced Form Styling === */
.form-control {
    border-radius: 12px !important;
    border: 2px solid #e2e8f0 !important;
    padding: 12px 16px !important;
    font-size: 1rem !important;
    transition: all 0.3s ease !important;
}

.form-control:focus {
    border-color: #6A1B9A !important;
    box-shadow: 0 0 0 4px rgba(106, 27, 154, 0.1) !important;
    transform: translateY(-1px) !important;
}

/* === Professional Table Styling === */
.table {
    border-radius: 12px !important;
    overflow: hidden !important;
}

.table thead th {
    background: linear-gradient(135deg, #f8fafc, #f1f5f9) !important;
    border: none !important;
    font-weight: 700 !important;
    font-size: 0.85rem !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    color: #475569 !important;
    padding: 1rem 1.5rem !important;
}

.table tbody tr {
    border: none !important;
    transition: all 0.2s ease !important;
}

.table tbody tr:hover {
    background: rgba(106, 27, 154, 0.03) !important;
    transform: scale(1.001) !important;
}

.table tbody td {
    border: none !important;
    padding: 1.25rem 1.5rem !important;
    vertical-align: middle !important;
}

/* === Professional Alerts === */
.alert {
    border: none !important;
    border-radius: 12px !important;
    backdrop-filter: blur(10px) !important;
}

.alert-warning {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 193, 7, 0.05)) !important;
    color: #856404 !important;
}

.alert-danger {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.1), rgba(220, 53, 69, 0.05)) !important;
    color: #721c24 !important;
}

/* === Professional Scrollbar === */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #6A1B9A, #FF9800);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #5e1688, #e68900);
}

/* ===== ENHANCED PAGINATION STYLES ===== */

/* Main table container */
.parcel-table-container {
    background: #ffffff !important;
    border-radius: 16px !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08) !important;
    overflow: hidden !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    backdrop-filter: blur(20px) !important;
}

/* Top controls bar */
.table-controls-top {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    padding: 1.25rem 2rem !important;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;
    border-bottom: 1px solid rgba(226, 232, 240, 0.6) !important;
}

.entries-control {
    display: flex !important;
    align-items: center !important;
    gap: 1rem !important;
}

.control-label {
    font-size: 0.9rem !important;
    font-weight: 600 !important;
    color: #64748b !important;
    letter-spacing: 0.025em !important;
}

.entries-select {
    padding: 0.5rem 1rem !important;
    border: 2px solid #e2e8f0 !important;
    border-radius: 10px !important;
    background: #ffffff !important;
    font-size: 0.9rem !important;
    font-weight: 600 !important;
    color: #374151 !important;
    min-width: 80px !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04) !important;
}

.entries-select:focus {
    outline: none !important;
    border-color: #6A1B9A !important;
    box-shadow: 0 0 0 4px rgba(106, 27, 154, 0.1) !important;
    transform: translateY(-1px) !important;
}

.entries-info {
    font-size: 0.9rem !important;
    font-weight: 600 !important;
    color: #6b7280 !important;
    letter-spacing: 0.025em !important;
}

/* Table wrapper */
.table-wrapper {
    background: #ffffff !important;
}

.table-header th {
    background: linear-gradient(135deg, #f8fafc, #f1f5f9) !important;
    color: #374151 !important;
    font-weight: 700 !important;
    font-size: 0.85rem !important;
    text-transform: uppercase !important;
    letter-spacing: 0.05em !important;
    border-bottom: 2px solid #e5e7eb !important;
    padding: 1.25rem 1.5rem !important;
}

/* Bottom controls bar */
.table-controls-bottom {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    padding: 1.25rem 2rem !important;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;
    border-top: 1px solid rgba(226, 232, 240, 0.6) !important;
}

.pagination-info {
    font-size: 0.9rem !important;
    font-weight: 600 !important;
    color: #6b7280 !important;
    letter-spacing: 0.025em !important;
}

/* Enhanced pagination controls */
.pagination-nav {
    margin: 0 !important;
}

.pagination-controls {
    display: flex !important;
    align-items: center !important;
    gap: 0.375rem !important;
    margin: 0 !important;
    padding: 0 !important;
    list-style: none !important;
}

.pagination-controls .page-item {
    margin: 0 !important;
}

.pagination-controls .page-link {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    min-width: 2.75rem !important;
    height: 2.75rem !important;
    padding: 0.625rem 0.875rem !important;
    font-size: 0.9rem !important;
    font-weight: 600 !important;
    color: #6b7280 !important;
    background: #ffffff !important;
    border: 2px solid #e2e8f0 !important;
    border-radius: 10px !important;
    text-decoration: none !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04) !important;
}

.pagination-controls .page-link:hover {
    color: #6A1B9A !important;
    background: rgba(106, 27, 154, 0.05) !important;
    border-color: rgba(106, 27, 154, 0.3) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(106, 27, 154, 0.15) !important;
}

.pagination-controls .page-item.active .page-link {
    color: #ffffff !important;
    background: linear-gradient(135deg, #6A1B9A 0%, #FF9800 100%) !important;
    border-color: transparent !important;
    box-shadow: 0 4px 16px rgba(106, 27, 154, 0.4) !important;
}

.pagination-controls .page-item.active .page-link:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 20px rgba(106, 27, 154, 0.5) !important;
}

.pagination-controls .page-item.disabled .page-link {
    color: #cbd5e1 !important;
    background: #f8fafc !important;
    border-color: #f1f5f9 !important;
    cursor: not-allowed !important;
    opacity: 0.6 !important;
}

.pagination-controls .page-item.disabled .page-link:hover {
    transform: none !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04) !important;
    color: #cbd5e1 !important;
    background: #f8fafc !important;
    border-color: #f1f5f9 !important;
}

/* Arrow buttons special styling */
.pagination-controls .page-link[aria-label="Previous"],
.pagination-controls .page-link[aria-label="Next"] {
    font-weight: 700 !important;
    min-width: 3rem !important;
    font-size: 1rem !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .table-controls-top,
    .table-controls-bottom {
        flex-direction: column !important;
        gap: 1rem !important;
        text-align: center !important;
        padding: 1rem 1.5rem !important;
    }

    .pagination-controls {
        flex-wrap: wrap !important;
        justify-content: center !important;
        gap: 0.25rem !important;
    }

    .pagination-controls .page-link {
        min-width: 2.5rem !important;
        height: 2.5rem !important;
        font-size: 0.85rem !important;
        padding: 0.5rem 0.75rem !important;
    }

    .pagination-controls .page-link[aria-label="Previous"],
    .pagination-controls .page-link[aria-label="Next"] {
        min-width: 2.75rem !important;
        font-size: 0.9rem !important;
    }
}

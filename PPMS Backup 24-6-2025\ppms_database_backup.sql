-- <PERSON><PERSON><PERSON> dump 10.19  Distrib 10.4.32-MariaD<PERSON>, for Win64 (AMD64)
--
-- Host: localhost    Database: ppms
-- ------------------------------------------------------
-- Server version       10.4.32-MariaDB

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `notification`
--

DROP TABLE IF EXISTS `notification`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `notification` (
  `notificationID` int(11) NOT NULL AUTO_INCREMENT,
  `ICNumber` varchar(20) NOT NULL,
  `TrackingNumber` varchar(50) DEFAULT NULL,
  `notificationType` varchar(50) DEFAULT NULL,
  `messageContent` text DEFAULT NULL,
  `sentTimestamp` datetime DEFAULT NULL,
  `notificationStatus` varchar(50) DEFAULT NULL,
  `isRead` tinyint(1) DEFAULT 0,
  `deliveryMethod` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`notificationID`),
  KEY `ICNumber` (`ICNumber`),
  KEY `TrackingNumber` (`TrackingNumber`),
  CONSTRAINT `notification_ibfk_1` FOREIGN KEY (`ICNumber`) REFERENCES `receiver` (`ICNumber`),
  CONSTRAINT `notification_ibfk_2` FOREIGN KEY (`TrackingNumber`) REFERENCES `parcel` (`TrackingNumber`)
) ENGINE=InnoDB AUTO_INCREMENT=26 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `notification`
--

LOCK TABLES `notification` WRITE;
/*!40000 ALTER TABLE `notification` DISABLE KEYS */;
INSERT INTO `notification` VALUES (8,'444444444444','GRT34534641','arrival','New parcel arrived! Your package (Tracking No: GRT34534641) is ready for pickup at TDI.','2025-06-21 05:59:31','sent',1,'system'),(9,'444444444444','TRR512566','arrival','New parcel arrived! Your package (Tracking No: TRR512566) is ready for pickup at HJH sukimah.','2025-06-21 17:43:49','sent',1,'system'),(10,'444444444444','TRR512566','pickup','Parcel collected! Your package (Tracking No: TRR512566) has been successfully retrieved. Thank you for using our service!','2025-06-21 17:44:56','sent',1,'system'),(11,'444444444444','MY098928221','arrival','New parcel arrived! Your package (Tracking No: MY098928221) is ready for pickup at T2-23A-01.','2025-06-21 18:05:23','sent',1,'system'),(18,'444444444444','MY098928221','pickup','Parcel collected! Your package (Tracking No: MY098928221) has been successfully retrieved. Thank you for using our service!','2025-06-22 04:57:37','sent',1,'system'),(19,'444444444444','GRT34534641','qr_email','QR verification code emailed to: <EMAIL>','2025-06-22 05:09:13','sent',1,'email'),(20,'444444444444','GRT34534641','pickup','Parcel collected! Your package (Tracking No: GRT34534641) has been successfully retrieved. Thank you for using our service!','2025-06-22 05:09:31','sent',1,'system'),(23,'010401150099','JNT90888607','arrival','New parcel arrived! Your package (Tracking No: JNT90888607) is ready for pickup at Jalan Desasiswa, Parit Sempadan Laut, 86400 Parit Raja, Johor Darul Ta\'zim.','2025-06-24 04:31:23','sent',1,'system'),(24,'010401150099','JNT90888607','qr_email','QR verification code emailed to: <EMAIL>','2025-06-24 05:09:47','sent',1,'email'),(25,'010401150099','JNT76572175','arrival','New parcel arrived! Your package (Tracking No: JNT76572175) is ready for pickup at Jalan Desasiswa, Parit Sempadan Laut, 86400 Parit Raja, Johor Darul Ta\'zim.','2025-06-24 05:58:21','sent',1,'system');
/*!40000 ALTER TABLE `notification` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `parcel`
--

DROP TABLE IF EXISTS `parcel`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `parcel` (
  `TrackingNumber` varchar(50) NOT NULL,
  `ICNumber` varchar(20) NOT NULL,
  `date` date DEFAULT NULL,
  `time` time DEFAULT NULL,
  `status` varchar(50) DEFAULT NULL,
  `name` varchar(100) DEFAULT NULL,
  `deliveryLocation` varchar(100) DEFAULT NULL,
  `QR` text DEFAULT NULL,
  `weight` decimal(8,2) DEFAULT NULL,
  PRIMARY KEY (`TrackingNumber`),
  KEY `ICNumber` (`ICNumber`),
  CONSTRAINT `parcel_ibfk_1` FOREIGN KEY (`ICNumber`) REFERENCES `receiver` (`ICNumber`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `parcel`
--

LOCK TABLES `parcel` WRITE;
/*!40000 ALTER TABLE `parcel` DISABLE KEYS */;
INSERT INTO `parcel` VALUES ('GRT34534641','444444444444','2025-06-21','05:59:31','Retrieved','parcel a bit wet','TDI',NULL,1.20),('JNT133546','444444444444','2025-06-21','05:13:30','Retrieved','ikan masin ke ni','TSN',NULL,4.00),('JNT76572175','010401150099','2025-06-24','05:58:21','Pending','Package','Jalan Desasiswa, Parit Sempadan Laut, 86400 Parit Raja, Johor Darul Ta\'zim',NULL,3.45),('JNT90888607','010401150099','2025-06-24','04:31:23','Pending','Sila ambil parcel anda dengan segera','Jalan Desasiswa, Parit Sempadan Laut, 86400 Parit Raja, Johor Darul Ta\'zim',NULL,3.50),('MY098928221','444444444444','2025-06-21','18:05:23','Retrieved','Package','T2-23A-01',NULL,12.04),('MY123123','444444444444','2025-06-21','04:24:22','Retrieved','parcel berat sikit','TDI',NULL,6.40),('SPX0987644','444444444444','2025-06-21','05:26:48','Retrieved','ini bomb ke apa?','TSN',NULL,51.02),('TRR512566','444444444444','2025-06-21','17:43:49','Retrieved','berat sikit hehe','HJH sukimah',NULL,12.00);
/*!40000 ALTER TABLE `parcel` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `receiver`
--

DROP TABLE IF EXISTS `receiver`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `receiver` (
  `ICNumber` varchar(20) NOT NULL,
  `name` varchar(100) NOT NULL,
  `phoneNumber` varchar(20) NOT NULL,
  `password` varchar(255) NOT NULL,
  `notificationID` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`ICNumber`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `receiver`
--

LOCK TABLES `receiver` WRITE;
/*!40000 ALTER TABLE `receiver` DISABLE KEYS */;
INSERT INTO `receiver` VALUES ('010101010101','ala','00000000000','$2y$10$81hijBHG5Cdn1NW3bcR.Suopew33eWr64TSP8ok7rMefP7kI1gc7.',NULL),('010203040506','Sayang','01115895859','$2y$10$LXkvgPZnAiyY9.CCSwjl9On9iLT4an3whza89E2UVi7QoZikc6yIu',NULL),('010401150077','Isz','01115895859','$2y$10$Ta2IKJvi2uPWSmh2epSqkOmvLNldoc1vr10nUVnDIxuami2301khi',NULL),('010401150099','Mikail','+601115895859','$2y$10$7fChdst60v9OCRZrhd1svu92fYjJYZpmPWchFvN9fg6jDt16RSCQC',NULL),('010401160070','Kiki','0113132235','$2y$10$rJQvUEa00A7Vm5JH81mcP.WrtBD2qMsM8U4DAH1DALGK4Z77fp5kC',NULL),('010401160092','gasfaru','1231235234','$2y$10$5A5IIujahGT82XmirubeleCtI1juZ5N/kZkEmEXRS8aQcrf0Nhmy6',NULL),('010401160099','gasfar','1231235234','$2y$10$/z5XVxIlJ1bVOov2ykW5DekSHWYlX/ekAtTx0FlpSFK80/I7mtczC',NULL),('020401150077','Mumu','1234567892','$2y$10$rOOcQPP/6NOcKIhWBbDiO.Iq3bzlFBdQ0GwZvjOMTU3XGJmFFwlJ6',NULL),('020927011234','Tyra','0912453434','$2y$10$fDrhIPoDiW5jXnMF.sJfOuJLhEtwB2pY9bkH0LsDrL1lmaQ4lcWYC',NULL),('029593900312','Pupu','9990010101','$2y$10$x38V70YOmrHXOG6RakKiu.ncgTpEUER0kSbKQrrTRuRwa2RwVofOi',NULL),('111111111111','test','00000000000','$2y$10$jtlEuev1vb3ARt8gIxtSx.kyALgHVw8ph71/CCPqWEf4GOthU2AyK',NULL),('121212121212','Isz','0987654232','$2y$10$kHSH8FLQ1MxbvaZVsU1z4Ozzf0jbCUioVcputJrUooSFa9zHXlmAO',NULL),('222222222222','test2','00000000000','$2y$10$mtGzgeKYnQGpAo7oEUfU8.YX/.2tDiF7xE8lAR9yYOijOxPk3hcLG',NULL),('245252423423','DMA isley','01131321231','$2y$10$F2oFiCV487J1n6sFwU8cteWXwYMpw7ePfmfHDbvcY5olHSy5AOdom',NULL),('444444444444','Madara','0193122525','$2y$10$m/IpHxiOJgpZUtVV.x1oIuVGxD78CHcOVM5bQE1Cit/LiStwd/IrO',NULL),('490495045440','Hamurami','01131321235','$2y$10$slxPvhYSVt1MJbLMYtzPLeKt9yO7aEVjXmTdOmsjwWHitvLCMvwC6',NULL),('490495222440','Hamuramo','01131321231','$2y$10$oteD/IdUeeyAHaEx2s4phu2h0LKRXdJtsg.BxiQnX6F0piTho2W/.',NULL);
/*!40000 ALTER TABLE `receiver` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `retrievalrecord`
--

DROP TABLE IF EXISTS `retrievalrecord`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `retrievalrecord` (
  `RetrievalID` int(11) NOT NULL AUTO_INCREMENT,
  `trackingNumber` varchar(50) NOT NULL,
  `ICNumber` varchar(20) NOT NULL,
  `staffID` varchar(20) DEFAULT NULL,
  `retrieveDate` date DEFAULT NULL,
  `status` varchar(50) DEFAULT NULL,
  `retrieveTime` time DEFAULT NULL,
  PRIMARY KEY (`RetrievalID`),
  KEY `trackingNumber` (`trackingNumber`),
  KEY `ICNumber` (`ICNumber`),
  KEY `staffID` (`staffID`),
  CONSTRAINT `retrievalrecord_ibfk_1` FOREIGN KEY (`trackingNumber`) REFERENCES `parcel` (`TrackingNumber`),
  CONSTRAINT `retrievalrecord_ibfk_2` FOREIGN KEY (`ICNumber`) REFERENCES `receiver` (`ICNumber`),
  CONSTRAINT `retrievalrecord_ibfk_3` FOREIGN KEY (`staffID`) REFERENCES `staff` (`staffID`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `retrievalrecord`
--

LOCK TABLES `retrievalrecord` WRITE;
/*!40000 ALTER TABLE `retrievalrecord` DISABLE KEYS */;
INSERT INTO `retrievalrecord` VALUES (1,'MY123123','444444444444',NULL,'2025-06-21','Retrieved','05:20:34'),(2,'JNT133546','444444444444',NULL,'2025-06-21','Retrieved','05:20:31'),(3,'SPX0987644','444444444444',NULL,'2025-06-21','Retrieved','05:29:17'),(5,'TRR512566','444444444444',NULL,'2025-06-21','Retrieved','17:44:56'),(6,'MY098928221','444444444444',NULL,'2025-06-22','Retrieved','04:57:37'),(7,'GRT34534641','444444444444',NULL,'2025-06-22','Retrieved','05:09:31');
/*!40000 ALTER TABLE `retrievalrecord` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `staff`
--

DROP TABLE IF EXISTS `staff`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `staff` (
  `staffID` varchar(20) NOT NULL,
  `role` varchar(50) NOT NULL,
  `name` varchar(100) NOT NULL,
  `phoneNumber` varchar(20) NOT NULL,
  `password` varchar(255) NOT NULL,
  PRIMARY KEY (`staffID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `staff`
--

LOCK TABLES `staff` WRITE;
/*!40000 ALTER TABLE `staff` DISABLE KEYS */;
INSERT INTO `staff` VALUES ('0105','Staff','Soloz Alfonso','','$2y$10$/ukobobdmfETZeru60hW3O9LvLho2RIGlge3eZP73lwvzJsjbigc6');
/*!40000 ALTER TABLE `staff` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-06-24 16:16:16
